#!/usr/bin/env python3
"""
Tarla tespitini düzelt - doğru threshold ve post-processing
"""

import rasterio
import numpy as np
import matplotlib.pyplot as plt
import cv2
from skimage import filters, morphology, measure, segmentation
from scipy import ndimage
import os

def fix_field_detection():
    """Tarla tespitini düzelt"""
    
    print("🔧 Tarla tespiti düzeltiliyor...")
    
    # Model çıktısını oku
    with rasterio.open("tiff_result/asd_center_5k_continuous.tif") as src:
        model_output = src.read(1)
        profile = src.profile
    
    print(f"📊 Model output: {model_output.shape}, range: {model_output.min():.3f} - {model_output.max():.3f}")
    
    # 1. Histogram analizi
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.hist(model_output.flatten(), bins=100, alpha=0.7)
    plt.title('Model Output Histogram')
    plt.xlabel('Value')
    plt.ylabel('Frequency')
    
    # 2. Farklı threshold'ları test et
    thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
    best_threshold = 0.3
    
    plt.subplot(1, 3, 2)
    for thresh in thresholds:
        binary = (model_output > thresh).astype(np.uint8)
        pixel_ratio = np.sum(binary) / binary.size
        print(f"   Threshold {thresh}: {pixel_ratio:.3f} ratio ({np.sum(binary)} pixels)")
        
        if 0.1 <= pixel_ratio <= 0.4:  # Reasonable field coverage
            best_threshold = thresh
    
    print(f"✅ En iyi threshold: {best_threshold}")
    
    # 3. En iyi threshold ile binary oluştur
    binary_mask = (model_output > best_threshold).astype(np.uint8) * 255
    
    plt.imshow(binary_mask, cmap='gray')
    plt.title(f'Binary (threshold={best_threshold})')
    plt.axis('off')
    
    # 4. Morphological operations
    plt.subplot(1, 3, 3)
    
    # Noise removal
    kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
    
    # Remove small noise
    cleaned = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel_small, iterations=2)
    
    # Fill small holes
    filled = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_medium, iterations=3)
    
    plt.imshow(filled, cmap='gray')
    plt.title('Cleaned Binary')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/threshold_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 5. Connected components analysis
    num_labels, labels = cv2.connectedComponents(filled)
    print(f"📊 Toplam bileşen sayısı: {num_labels-1}")
    
    # Area filtering
    min_area = 1000  # Minimum 1000 pixels (daha küçük threshold)
    max_area = 500000  # Maximum area to avoid huge regions
    
    filtered_labels = np.zeros_like(labels)
    valid_areas = []
    label_counter = 1
    
    for label_id in range(1, num_labels):
        mask = (labels == label_id)
        area = np.sum(mask)
        
        if min_area <= area <= max_area:
            filtered_labels[mask] = label_counter
            valid_areas.append(area)
            label_counter += 1
    
    print(f"✅ {label_counter-1} geçerli tarla alanı bulundu")
    print(f"📊 Alan dağılımı: {np.min(valid_areas) if valid_areas else 0} - {np.max(valid_areas) if valid_areas else 0} pixels")
    
    # 6. Additional morphological refinement
    print("🔧 Ek morphological işlemler uygulanıyor...")

    # Daha büyük kernel ile son temizlik
    kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))

    # Son temizlik
    final_cleaned = cv2.morphologyEx(filled, cv2.MORPH_CLOSE, kernel_large, iterations=2)
    final_cleaned = cv2.morphologyEx(final_cleaned, cv2.MORPH_OPEN, kernel_medium, iterations=1)

    # Yeniden connected components
    num_labels_final, labels_final = cv2.connectedComponents(final_cleaned)

    # Final filtering
    final_labels = np.zeros_like(labels_final)
    final_counter = 1

    for label_id in range(1, num_labels_final):
        mask = (labels_final == label_id)
        area = np.sum(mask)

        if min_area <= area <= max_area:
            final_labels[mask] = final_counter
            final_counter += 1

    final_count = final_counter - 1
    print(f"✅ Final işlem ile {final_count} tarla alanı bulundu")
    
    # 7. Görselleştirme
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Original model output
    axes[0, 0].imshow(model_output, cmap='viridis', vmin=0, vmax=1)
    axes[0, 0].set_title('Model Output')
    axes[0, 0].axis('off')
    
    # Binary mask
    axes[0, 1].imshow(binary_mask, cmap='gray')
    axes[0, 1].set_title(f'Binary (thresh={best_threshold})')
    axes[0, 1].axis('off')
    
    # Cleaned binary
    axes[0, 2].imshow(filled, cmap='gray')
    axes[0, 2].set_title('Cleaned Binary')
    axes[0, 2].axis('off')
    
    # Connected components
    axes[1, 0].imshow(labels, cmap='tab20')
    axes[1, 0].set_title(f'All Components ({num_labels-1})')
    axes[1, 0].axis('off')
    
    # Filtered components
    axes[1, 1].imshow(filtered_labels, cmap='tab20')
    axes[1, 1].set_title(f'Filtered ({label_counter-1} fields)')
    axes[1, 1].axis('off')
    
    # Final result with colors
    colored_fields = np.zeros((final_labels.shape[0], final_labels.shape[1], 3), dtype=np.uint8)
    
    if final_count > 0:
        # Generate distinct colors
        colors = plt.cm.Set3(np.linspace(0, 1, final_count))[:, :3] * 255
        
        for label_id in range(1, final_count + 1):
            mask = (final_labels == label_id)
            if label_id <= len(colors):
                colored_fields[mask] = colors[label_id-1]
    
    axes[1, 2].imshow(colored_fields)
    axes[1, 2].set_title(f'Final Fields ({final_count} fields)')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/fixed_field_detection.png', dpi=150, bbox_inches='tight')
    print("💾 Düzeltilmiş tespit kaydedildi: tiff_result/fixed_field_detection.png")
    plt.show()
    
    # 8. Sonuçları kaydet
    # Binary mask
    binary_profile = profile.copy()
    binary_profile.update(dtype='uint8', count=1)
    
    with rasterio.open("tiff_result/asd_center_5k_binary_fixed.tif", 'w', **binary_profile) as dst:
        dst.write(filled, 1)
    
    # Field labels
    labels_profile = profile.copy()
    labels_profile.update(dtype='uint16', count=1)
    
    with rasterio.open("tiff_result/asd_center_5k_fields.tif", 'w', **labels_profile) as dst:
        dst.write(final_labels.astype(np.uint16), 1)
    
    # Colored visualization
    rgb_profile = profile.copy()
    rgb_profile.update(dtype='uint8', count=3)
    
    with rasterio.open("tiff_result/asd_center_5k_colored.tif", 'w', **rgb_profile) as dst:
        for i in range(3):
            dst.write(colored_fields[:, :, i], i+1)
    
    print("💾 Tüm sonuçlar kaydedildi:")
    print("   - asd_center_5k_binary_fixed.tif (düzeltilmiş binary)")
    print("   - asd_center_5k_fields.tif (tarla etiketleri)")
    print("   - asd_center_5k_colored.tif (renkli görselleştirme)")
    
    return final_labels, colored_fields, final_count

def compare_with_original():
    """Orijinal ile karşılaştır"""
    
    print("🔍 Orijinal ile karşılaştırma...")
    
    # Orijinal görüntü
    with rasterio.open("geotiff_input/asd_center_5k.tif") as src:
        original = src.read()
        if original.shape[0] <= 4:
            original = np.transpose(original, (1, 2, 0))
    
    # Renkli tarla sonucu
    with rasterio.open("tiff_result/asd_center_5k_colored.tif") as src:
        colored_result = src.read()
        if colored_result.shape[0] <= 4:
            colored_result = np.transpose(colored_result, (1, 2, 0))
    
    # Karşılaştırmalı görselleştirme
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    
    # Orijinal
    if original.max() > 1:
        original_vis = original / 255.0
    else:
        original_vis = original
    
    axes[0].imshow(original_vis)
    axes[0].set_title('Orijinal Görüntü\n(Tarla sınırları net görülüyor)')
    axes[0].axis('off')
    
    # Sonuç
    axes[1].imshow(colored_result)
    axes[1].set_title('Tespit Edilen Tarlalar\n(SAM-Adapter sonucu)')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/original_vs_result.png', dpi=150, bbox_inches='tight')
    print("💾 Karşılaştırma kaydedildi: tiff_result/original_vs_result.png")
    plt.show()

if __name__ == "__main__":
    # Tarla tespitini düzelt
    final_labels, colored_fields, field_count = fix_field_detection()
    
    # Orijinal ile karşılaştır
    compare_with_original()
    
    print(f"✅ Düzeltme tamamlandı! {field_count} tarla alanı tespit edildi.")
