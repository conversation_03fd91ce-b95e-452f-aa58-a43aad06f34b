import argparse
import os

import yaml
from tqdm import tqdm
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR

import datasets
import models
import utils
from statistics import mean
import torch
import torch.distributed as dist

torch.distributed.init_process_group(backend='nccl')
local_rank = torch.distributed.get_rank()
torch.cuda.set_device(local_rank)
device = torch.device("cuda", local_rank)


def make_data_loader(spec, tag=''):
    if spec is None:
        return None

    dataset = datasets.make(spec['dataset'])
    dataset = datasets.make(spec['wrapper'], args={'dataset': dataset})
    if local_rank == 0:
        log('{} dataset: size={}'.format(tag, len(dataset)))
        for k, v in dataset[0].items():
            log('  {}: shape={}'.format(k, tuple(v.shape)))

    sampler = torch.utils.data.distributed.DistributedSampler(dataset)
    loader = DataLoader(dataset, batch_size=spec['batch_size'],
        shuffle=False, num_workers=8, pin_memory=True, sampler=sampler)
    return loader


def make_data_loaders():
    train_loader = make_data_loader(config.get('train_dataset'), tag='train')
    val_loader = make_data_loader(config.get('val_dataset'), tag='val')
    return train_loader, val_loader


def eval_psnr(loader, model, eval_type=None):
    model.eval()

    # Agresif bellek temizliği
    torch.cuda.empty_cache()

    # Metrik fonksiyonunu belirle
    if eval_type == 'f1':
        metric_fn = utils.calc_f1
        metric1, metric2, metric3, metric4 = 'f1', 'auc', 'none', 'none'
    elif eval_type == 'fmeasure':
        metric_fn = utils.calc_fmeasure
        metric1, metric2, metric3, metric4 = 'f_mea', 'mae', 'none', 'none'
    elif eval_type == 'ber':
        metric_fn = utils.calc_ber
        metric1, metric2, metric3, metric4 = 'shadow', 'non_shadow', 'ber', 'none'
    elif eval_type == 'cod':
        metric_fn = utils.calc_cod
        metric1, metric2, metric3, metric4 = 'sm', 'em', 'wfm', 'mae'

    if local_rank == 0:
        pbar = tqdm(total=len(loader), leave=False, desc='val')
    else:
        pbar = None

    # Belleği temizle
    torch.cuda.empty_cache()

    # Daha küçük parçalar halinde işle
    pred_chunks = []
    gt_chunks = []
    chunk_size = 10  # Daha küçük parçalar halinde işle
    current_chunk_preds = []
    current_chunk_gts = []
    chunk_count = 0

    with torch.no_grad():  # Gradient hesaplamayı devre dışı bırak
        for batch in loader:
            # Belleği her 10 batch'te bir temizle
            if chunk_count % 10 == 0:
                torch.cuda.empty_cache()

            for k, v in batch.items():
                batch[k] = v.cuda()

            inp = batch['inp']
            pred = torch.sigmoid(model.infer(inp))

            # Geçici değişkenleri temizle
            del inp

            batch_pred = [torch.zeros_like(pred) for _ in range(dist.get_world_size())]
            batch_gt = [torch.zeros_like(batch['gt']) for _ in range(dist.get_world_size())]

            dist.all_gather(batch_pred, pred)
            current_chunk_preds.extend(batch_pred)
            dist.all_gather(batch_gt, batch['gt'])
            current_chunk_gts.extend(batch_gt)

            # Geçici değişkenleri temizle
            del pred, batch_pred, batch_gt, batch

            chunk_count += 1

            # Chunk tamamlandığında işle ve belleği temizle
            if chunk_count % chunk_size == 0:
                if current_chunk_preds:
                    pred_chunks.append(torch.cat(current_chunk_preds, 1))
                    gt_chunks.append(torch.cat(current_chunk_gts, 1))
                    del current_chunk_preds, current_chunk_gts
                    current_chunk_preds = []
                    current_chunk_gts = []
                    torch.cuda.empty_cache()

            if pbar is not None:
                pbar.update(1)

    # Son chunk'ı işle
    if current_chunk_preds:
        pred_chunks.append(torch.cat(current_chunk_preds, 1))
        gt_chunks.append(torch.cat(current_chunk_gts, 1))
        del current_chunk_preds, current_chunk_gts
        torch.cuda.empty_cache()

    if pbar is not None:
        pbar.close()

    # Belleği tekrar temizle
    torch.cuda.empty_cache()

    # Tüm chunk'ları birleştir
    pred_list = torch.cat(pred_chunks, 1) if pred_chunks else None
    gt_list = torch.cat(gt_chunks, 1) if gt_chunks else None

    # Chunk listelerini temizle
    del pred_chunks, gt_chunks
    torch.cuda.empty_cache()

    # Sonuçları hesapla
    result1, result2, result3, result4 = metric_fn(pred_list, gt_list)

    # Son temizlik
    del pred_list, gt_list
    torch.cuda.empty_cache()

    return result1, result2, result3, result4, metric1, metric2, metric3, metric4


def prepare_training():
    if local_rank == 0:
        print(f"Preparing training, save_path: {save_path}")

    # Eğer resume parametresi belirtilmemişse, son epoch'u kontrol et
    if config.get('resume') is None:
        last_epoch_file = os.path.join(save_path, "last_epoch.txt")
        if os.path.exists(last_epoch_file):
            try:
                with open(last_epoch_file, "r") as f:
                    last_epoch = int(f.read().strip())
                    config['resume'] = last_epoch
                    if local_rank == 0:
                        print(f"Automatically resuming from epoch {last_epoch}")
            except Exception as e:
                if local_rank == 0:
                    print(f"Error reading last_epoch.txt: {e}")

    if local_rank == 0:
        print(f"Resume parameter: {config.get('resume')}")

    if config.get('resume') is not None:
        if local_rank == 0:
            print("Resuming training from checkpoint")

        model = models.make(config['model']).cuda()
        optimizer = utils.make_optimizer(
            model.parameters(), config['optimizer'])

        # Checkpoint dosyasını kontrol et
        checkpoint_path = os.path.join(save_path, f"checkpoint_last.pth")
        if os.path.exists(checkpoint_path):
            if local_rank == 0:
                print(f"Found checkpoint file: {checkpoint_path}")

            epoch_start = load_checkpoint(checkpoint_path, model, optimizer)
            if epoch_start is None:
                if local_rank == 0:
                    print("Failed to load checkpoint, starting from scratch")
                epoch_start = 1
            else:
                if local_rank == 0:
                    print(f"Loaded checkpoint from epoch {epoch_start}")
        else:
            if local_rank == 0:
                print(f"Checkpoint file not found: {checkpoint_path}")

            # Eski model dosyasını kontrol et
            model_path = os.path.join(save_path, f"model_epoch_last.pth")
            if os.path.exists(model_path):
                if local_rank == 0:
                    print(f"Found model file: {model_path}")

                try:
                    model.load_state_dict(torch.load(model_path))
                    if local_rank == 0:
                        print(f"Loaded model from {model_path}")
                except Exception as e:
                    if local_rank == 0:
                        print(f"Error loading model: {e}")
            else:
                if local_rank == 0:
                    print(f"Model file not found: {model_path}")

            # Optimizer durumunu yükle (eğer kaydedildiyse)
            if 'sd' in config['optimizer']:
                try:
                    optimizer.load_state_dict(config['optimizer']['sd'])
                    if local_rank == 0:
                        print("Loaded optimizer state from config")
                except Exception as e:
                    if local_rank == 0:
                        print(f"Error loading optimizer state: {e}")

            epoch_start = config.get('resume') + 1
    else:
        if local_rank == 0:
            print("Starting training from scratch")

        model = models.make(config['model']).cuda()
        optimizer = utils.make_optimizer(
            model.parameters(), config['optimizer'])
        epoch_start = 1

    max_epoch = config.get('epoch_max')
    lr_scheduler = CosineAnnealingLR(optimizer, max_epoch, eta_min=config.get('lr_min'))

    if local_rank == 0:
        log('model: #params={}'.format(utils.compute_num_params(model, text=True)))
        print(f"Starting from epoch {epoch_start}, max_epoch: {max_epoch}")

    return model, optimizer, epoch_start, lr_scheduler


def train(train_loader, model):
    model.train()

    if local_rank == 0:
        pbar = tqdm(total=len(train_loader), leave=False, desc='train')
    else:
        pbar = None

    loss_list = []
    for batch in train_loader:
        for k, v in batch.items():
            batch[k] = v.to(device)
        inp = batch['inp']
        gt = batch['gt']
        model.set_input(inp, gt)
        model.optimize_parameters()
        batch_loss = [torch.zeros_like(model.loss_G) for _ in range(dist.get_world_size())]
        dist.all_gather(batch_loss, model.loss_G)
        loss_list.extend(batch_loss)
        if pbar is not None:
            pbar.update(1)

    if pbar is not None:
        pbar.close()

    loss = [i.item() for i in loss_list]
    return mean(loss)


def main(config_, save_path, args):
    global config, log, writer, log_info
    config = config_

    # Klasörün var olduğundan emin ol
    os.makedirs(save_path, exist_ok=True)

    if local_rank == 0:
        print(f"Starting main function, save_path: {save_path}")

    log, writer = utils.set_save_path(save_path, remove=False)

    # Eğer resume parametresi belirtilmemişse, son epoch'u kontrol et
    if config.get('resume') is None:
        last_epoch_file = os.path.join(save_path, "last_epoch.txt")
        if os.path.exists(last_epoch_file):
            try:
                with open(last_epoch_file, "r") as f:
                    content = f.read().strip()
                    if local_rank == 0:
                        print(f"Read from last_epoch.txt: '{content}'")
                    last_epoch = int(content)
                    config['resume'] = last_epoch
                    if local_rank == 0:
                        print(f"Automatically resuming from epoch {last_epoch}")
            except Exception as e:
                if local_rank == 0:
                    print(f"Error reading last_epoch.txt: {e}")
                    import traceback
                    traceback.print_exc()

    # Config dosyasını kaydet
    config_file = os.path.join(save_path, 'config.yaml')
    try:
        with open(config_file, 'w') as f:
            yaml.dump(config, f, sort_keys=False)
        if local_rank == 0:
            print(f"Saved config to {config_file}")
    except Exception as e:
        if local_rank == 0:
            print(f"Error saving config: {e}")
            import traceback
            traceback.print_exc()

    train_loader, val_loader = make_data_loaders()
    if config.get('data_norm') is None:
        config['data_norm'] = {
            'inp': {'sub': [0], 'div': [1]},
            'gt': {'sub': [0], 'div': [1]}
        }

    model, optimizer, epoch_start, lr_scheduler = prepare_training()
    model.optimizer = optimizer
    lr_scheduler = CosineAnnealingLR(model.optimizer, config['epoch_max'], eta_min=config.get('lr_min'))

    model = model.cuda()
    model = torch.nn.parallel.DistributedDataParallel(
        model,
        device_ids=[args.local_rank],
        output_device=args.local_rank,
        find_unused_parameters=True,
        broadcast_buffers=False
    )
    model = model.module

    sam_checkpoint = torch.load(config['sam_checkpoint'])
    model.load_state_dict(sam_checkpoint, strict=False)

    for name, para in model.named_parameters():
        if "image_encoder" in name and "prompt_generator" not in name:
            para.requires_grad_(False)
    if local_rank == 0:
        model_total_params = sum(p.numel() for p in model.parameters())
        model_grad_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print('model_grad_params:' + str(model_grad_params), '\nmodel_total_params:' + str(model_total_params))

    epoch_max = config['epoch_max']
    epoch_val = config.get('epoch_val')
    max_val_v = -1e18 if config['eval_type'] != 'ber' else 1e8
    timer = utils.Timer()
    for epoch in range(epoch_start, epoch_max + 1):
        # Mevcut epoch'u config'e kaydet
        config['current_epoch'] = epoch

        train_loader.sampler.set_epoch(epoch)
        t_epoch_start = timer.t()
        train_loss_G = train(train_loader, model)
        lr_scheduler.step()

        if local_rank == 0:
            log_info = ['epoch {}/{}'.format(epoch, epoch_max)]
            writer.add_scalar('lr', optimizer.param_groups[0]['lr'], epoch)
            log_info.append('train G: loss={:.4f}'.format(train_loss_G))
            writer.add_scalars('loss', {'train G': train_loss_G}, epoch)

            # Model ve optimizer durumlarını config'e kaydet
            try:
                model_spec = config['model']
                model_spec['sd'] = model.state_dict()
                optimizer_spec = config['optimizer']
                optimizer_spec['sd'] = optimizer.state_dict()
                if local_rank == 0:
                    print(f"Saved model and optimizer states to config")
            except Exception as e:
                if local_rank == 0:
                    print(f"Error saving states to config: {e}")

            # Her epoch sonunda model ve checkpoint kaydet
            if epoch % config.get('epoch_save', 1) == 0:
                save(config, model, save_path, 'last')
                save_checkpoint(model, optimizer, epoch, save_path, 'last')

                # Her 10% eğitimde bir ara checkpoint kaydet
                if epoch % max(1, epoch_max // 10) == 0:
                    save(config, model, save_path, f'{epoch}')
                    save_checkpoint(model, optimizer, epoch, save_path, f'{epoch}')

        if (epoch_val is not None) and (epoch % epoch_val == 0):
            # Validation öncesi belleği temizle
            torch.cuda.empty_cache()

            # Validation işlemi
            result1, result2, result3, result4, metric1, metric2, metric3, metric4 = eval_psnr(val_loader, model,
                eval_type=config.get('eval_type'))

            # Validation sonrası belleği temizle
            torch.cuda.empty_cache()

            if local_rank == 0:
                log_info.append('val: {}={:.4f}'.format(metric1, result1))
                writer.add_scalars(metric1, {'val': result1}, epoch)
                log_info.append('val: {}={:.4f}'.format(metric2, result2))
                writer.add_scalars(metric2, {'val': result2}, epoch)
                log_info.append('val: {}={:.4f}'.format(metric3, result3))
                writer.add_scalars(metric3, {'val': result3}, epoch)
                log_info.append('val: {}={:.4f}'.format(metric4, result4))
                writer.add_scalars(metric4, {'val': result4}, epoch)

                if config['eval_type'] != 'ber':
                    if result1 > max_val_v:
                        max_val_v = result1
                        save(config, model, save_path, 'best')
                else:
                    if result3 < max_val_v:
                        max_val_v = result3
                        save(config, model, save_path, 'best')

                t = timer.t()
                prog = (epoch - epoch_start + 1) / (epoch_max - epoch_start + 1)
                t_epoch = utils.time_text(t - t_epoch_start)
                t_elapsed, t_all = utils.time_text(t), utils.time_text(t / prog)
                log_info.append('{} {}/{}'.format(t_epoch, t_elapsed, t_all))

                log(', '.join(log_info))
                writer.flush()

    # Eğitim sonunda son modeli kaydet
    if local_rank == 0:
        print("Training completed, saving final model and checkpoint")
        save(config, model, save_path, 'final')
        save_checkpoint(model, optimizer, epoch_max, save_path, 'final')
        print(f"Final model and checkpoint saved to {save_path}")


def save(config, model, save_path, name):
    # Klasörün var olduğundan emin ol
    os.makedirs(save_path, exist_ok=True)

    if local_rank == 0:
        print(f"Saving model to {save_path}, name: {name}")
        print(f"Model type: {config['model']['name']}")

    try:
        if config['model']['name'] == 'segformer' or config['model']['name'] == 'setr':
            if config['model']['args']['encoder_mode']['name'] == 'evp':
                prompt_generator = model.encoder.backbone.prompt_generator.state_dict()
                decode_head = model.encoder.decode_head.state_dict()
                save_path_file = os.path.join(save_path, f"prompt_epoch_{name}.pth")
                torch.save({"prompt": prompt_generator, "decode_head": decode_head}, save_path_file)
                if local_rank == 0:
                    print(f"Saved prompt model to {save_path_file}")
            else:
                save_path_file = os.path.join(save_path, f"model_epoch_{name}.pth")
                torch.save(model.state_dict(), save_path_file)
                if local_rank == 0:
                    print(f"Saved segformer/setr model to {save_path_file}")
        else:
            save_path_file = os.path.join(save_path, f"model_epoch_{name}.pth")
            torch.save(model.state_dict(), save_path_file)
            if local_rank == 0:
                print(f"Saved model to {save_path_file}")

        # Son epoch'u kaydet
        current_epoch = config.get('current_epoch', 0)
        last_epoch_file = os.path.join(save_path, "last_epoch.txt")
        with open(last_epoch_file, "w") as f:
            f.write(str(current_epoch))
        if local_rank == 0:
            print(f"Saved last epoch ({current_epoch}) to {last_epoch_file}")
    except Exception as e:
        if local_rank == 0:
            print(f"Error saving model: {e}")
            import traceback
            traceback.print_exc()


def save_checkpoint(model, optimizer, epoch, save_path, name):
    # Klasörün var olduğundan emin ol
    os.makedirs(save_path, exist_ok=True)

    if local_rank == 0:
        print(f"Saving checkpoint to {save_path}, name: {name}, epoch: {epoch}")

    try:
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
        }
        checkpoint_file = os.path.join(save_path, f"checkpoint_{name}.pth")
        torch.save(checkpoint, checkpoint_file)
        if local_rank == 0:
            print(f"Saved checkpoint to {checkpoint_file}")

        # Son epoch'u ayrı bir dosyaya kaydet
        last_epoch_file = os.path.join(save_path, "last_epoch.txt")
        with open(last_epoch_file, "w") as f:
            f.write(str(epoch))
        if local_rank == 0:
            print(f"Saved last epoch ({epoch}) to {last_epoch_file}")
    except Exception as e:
        if local_rank == 0:
            print(f"Error saving checkpoint: {e}")
            import traceback
            traceback.print_exc()


def load_checkpoint(checkpoint_path, model, optimizer):
    if local_rank == 0:
        print(f"Loading checkpoint from {checkpoint_path}")

    try:
        # Belleği temizle
        torch.cuda.empty_cache()

        # Checkpoint'in var olduğunu kontrol et
        if not os.path.exists(checkpoint_path):
            if local_rank == 0:
                print(f"Checkpoint file {checkpoint_path} does not exist!")
            return None

        # Checkpoint'i CPU'ya yükle, daha sonra gerektiğinde GPU'ya taşı
        checkpoint = torch.load(checkpoint_path, map_location='cpu')

        if local_rank == 0:
            print(f"Checkpoint loaded, epoch: {checkpoint['epoch']}")

        # Model durumunu yükle
        model.load_state_dict(checkpoint['model_state_dict'])

        # Optimizer durumunu yükle
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # Belleği tekrar temizle
        torch.cuda.empty_cache()

        if local_rank == 0:
            print(f"Successfully loaded checkpoint from epoch {checkpoint['epoch']}")

        return checkpoint['epoch']
    except Exception as e:
        if local_rank == 0:
            print(f"Error loading checkpoint: {e}")
            import traceback
            traceback.print_exc()
        return None


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', default="configs/train/setr/train_setr_evp_cod.yaml")
    parser.add_argument('--name', default=None)
    parser.add_argument('--tag', default=None)
    parser.add_argument("--local_rank", type=int, default=-1, help="")
    args = parser.parse_args()

    with open(args.config, 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
        if local_rank == 0:
            print('config loaded.')

    save_name = args.name
    if save_name is None:
        save_name = '_' + args.config.split('/')[-1][:-len('.yaml')]
    if args.tag is not None:
        save_name += '_' + args.tag
    save_path = os.path.join('./save', save_name)

    main(config, save_path, args=args)
