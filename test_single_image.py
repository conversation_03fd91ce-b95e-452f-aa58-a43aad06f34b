#!/usr/bin/env python3
"""
Tek görüntü test scripti - Tarla bazlı renklendirme test
"""

import os
import sys
from fast_batch_processor import ExactTestResultProcessor

def test_single_image():
    """Tek görüntü üzerinde tarla bazlı renklendirme test et"""
    
    # Test görüntüsü - beyaz alanları test et
    test_image = "test_images/DJI_0067.JPG"  # Son görüntü (daha fazla beyaz alan olabilir)
    
    if not os.path.exists(test_image):
        print(f"❌ Test görüntüsü bulunamadı: {test_image}")
        return
    
    print("🌾 TEK GÖRÜNTÜ EKİN TÜRÜ BAZLI RENKLENDİRME TESTİ")
    print("=" * 60)
    print(f"📷 Test görüntüsü: {test_image}")
    print(f"📁 Çıktı klasörü: test_output")
    print("🎯 Aynı tarlada farklı ekinler farklı renklerde olacak!")

    # Processor oluştur - EKİN TÜRÜ BAZLI GRUPLAMA
    processor = ExactTestResultProcessor(
        output_dir='test_output',
        num_color_groups=25,  # 25 farklı ekin türü/durumu
        filter_edge_areas=False  # Kenar filtreleme kapalı
    )

    # EKİN TÜRÜ BAZLI parametreleri aktif et
    processor.color_params['use_crop_based_grouping'] = True
    processor.color_params['merge_similar_groups'] = True
    processor.color_params['max_groups'] = 25
    processor.color_params['allow_mixed_fields'] = True
    
    # Tek görüntüyü işle
    try:
        result = processor.process_single_image(test_image)
        
        if result:
            print("\n✅ İşlem başarılı!")
            print(f"📊 Tespit edilen tarla sayısı: {result.get('num_fields', 0)}")
            print(f"🎯 Tarla kapsaması: {result.get('field_coverage', 0):.1f}%")
            print(f"📏 Ortalama tarla boyutu: {result.get('avg_field_size', 0):.0f} piksel²")
            
            # Çıktı dosyasını göster
            output_file = os.path.join('test_output', 'DJI_0067_with_model_output.jpg')
            if os.path.exists(output_file):
                print(f"🖼️ Sonuç dosyası: {output_file}")
                print("🌐 Tarayıcıda açmak için:")
                print(f"   file:///{os.path.abspath(output_file).replace(os.sep, '/')}")
        else:
            print("❌ İşlem başarısız!")
            
    except Exception as e:
        print(f"❌ Hata: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_image()
