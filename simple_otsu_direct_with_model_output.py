import os
import torch
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
from torchvision import transforms
import cv2
import random

# SAM-Adapter imports
import yaml
from models import make as model_make
from models import sam  # SAM model register edilsin
import utils

def load_sam_adapter_model():
    """Load the trained SAM-Adapter model"""
    config_path = 'configs/cod-sam-vit-h.yaml'
    checkpoint_path = 'save/checkpoint_14.pth'
    
    # Load config
    with open(config_path, 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    
    # Create model
    model = model_make(config['model']).cuda()
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Check if checkpoint contains model_state_dict
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'], strict=True)
    else:
        model.load_state_dict(checkpoint, strict=True)
    
    # Set to eval mode
    model.eval()
    
    return model

def preprocess_image(image_path):
    """Preprocess image for SAM-Adapter inference"""
    # Read image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize to model input size (1024x1024 for SAM)
    image_resized = cv2.resize(image, (1024, 1024))
    
    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    
    image_tensor = transform(image_resized).unsqueeze(0).cuda()
    
    return image_tensor, image, image_resized

def inference_sam_adapter(model, image_tensor):
    """Run inference with SAM-Adapter model"""
    with torch.no_grad():
        # SAM model için input normalizasyonu
        # Normalize to [-1, 1] as expected by SAM
        normalized_input = (image_tensor - 0.5) / 0.5
        
        # Set model input
        model.set_input(normalized_input, normalized_input)  # dummy gt for inference
        
        # Forward pass
        model.forward()
        
        # Get prediction
        prediction = model.pred_mask
        
        # Convert to numpy
        if prediction.dim() == 4:
            prediction = prediction.squeeze(0).squeeze(0)
        elif prediction.dim() == 3:
            prediction = prediction.squeeze(0)
        
        # Apply sigmoid to get probabilities
        prediction = torch.sigmoid(prediction)
        
        return prediction.cpu().numpy()

def calculate_local_otsu_threshold(window):
    """Calculate Otsu threshold for a window"""
    if window.size == 0 or window.std() == 0:
        return 0.5
    
    # Convert to 8-bit for Otsu
    window_8bit = (window * 255).astype(np.uint8)
    
    try:
        threshold_val, _ = cv2.threshold(window_8bit, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return threshold_val / 255.0
    except:
        return 0.5

def simple_otsu_sliding_window_32x32(prediction):
    """Simple and successful Otsu 32x32 sliding window method"""
    print("🎯 Simple Otsu 32x32 Sliding Window (ORIGINAL SUCCESSFUL METHOD)")
    
    height, width = prediction.shape
    window_size = 32
    step_size = 16  # 50% overlap
    
    threshold_map = np.zeros_like(prediction)
    weight_map = np.zeros_like(prediction)
    
    # Slide window across image
    for y in range(0, height - window_size + 1, step_size):
        for x in range(0, width - window_size + 1, step_size):
            window = prediction[y:y+window_size, x:x+window_size]
            
            # Calculate Otsu threshold for this window
            local_threshold = calculate_local_otsu_threshold(window)
            
            threshold_map[y:y+window_size, x:x+window_size] += local_threshold
            weight_map[y:y+window_size, x:x+window_size] += 1
    
    # Handle uncovered regions
    uncovered_mask = weight_map == 0
    if uncovered_mask.any():
        global_threshold = calculate_local_otsu_threshold(prediction)
        threshold_map[uncovered_mask] = global_threshold
        weight_map[uncovered_mask] = 1
    
    # Average overlapping regions
    threshold_map = threshold_map / weight_map
    
    # Apply threshold
    binary_mask = (prediction > threshold_map).astype(np.uint8) * 255
    
    # Simple morphological cleanup
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel, iterations=1)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel, iterations=1)
    
    return binary_mask, threshold_map

def generate_colors(n):
    """Generate n distinct colors"""
    colors = []
    for i in range(n):
        # HSV to RGB for better color distribution
        hue = (i * 137.5) % 360  # Golden angle for good distribution
        saturation = 70 + (i % 3) * 10  # 70-90%
        value = 80 + (i % 3) * 10       # 80-100%
        
        # Convert HSV to RGB
        import colorsys
        r, g, b = colorsys.hsv_to_rgb(hue/360, saturation/100, value/100)
        colors.append((int(r*255), int(g*255), int(b*255)))
    
    return colors

def draw_field_areas_on_original(original_image, binary_mask, image_name):
    """Draw detected field areas on original image with different colors"""
    print(f"Tarla alanları çiziliyor: {image_name}")
    
    # Resize binary mask to match original image size
    original_height, original_width = original_image.shape[:2]
    binary_mask_resized = cv2.resize(binary_mask, (original_width, original_height))
    
    # Find contours
    contours, hierarchy = cv2.findContours(binary_mask_resized, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours by area (remove very small ones)
    min_area = (original_width * original_height) * 0.005  # Minimum 0.5% of image
    filtered_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
    
    print(f"  Toplam tarla alanı sayısı: {len(filtered_contours)}")
    
    # Generate colors for each field
    colors = generate_colors(len(filtered_contours))
    
    # Create visualizations
    visualizations = {}
    
    # 1. Filled areas with transparency
    filled_overlay = original_image.copy()
    for i, contour in enumerate(filtered_contours):
        color = colors[i % len(colors)]
        cv2.fillPoly(filled_overlay, [contour], color)
    filled_result = cv2.addWeighted(original_image, 0.6, filled_overlay, 0.4, 0)
    visualizations['filled'] = filled_result
    
    # 2. Contour outlines only (thick)
    contour_result = original_image.copy()
    for i, contour in enumerate(filtered_contours):
        color = colors[i % len(colors)]
        cv2.drawContours(contour_result, [contour], -1, color, thickness=8)
    visualizations['contour_thick'] = contour_result
    
    # 3. Contour outlines only (thin)
    contour_thin_result = original_image.copy()
    for i, contour in enumerate(filtered_contours):
        color = colors[i % len(colors)]
        cv2.drawContours(contour_thin_result, [contour], -1, color, thickness=3)
    visualizations['contour_thin'] = contour_thin_result
    
    # 4. Numbered areas
    numbered_result = filled_result.copy()
    for i, contour in enumerate(filtered_contours):
        # Calculate centroid
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            
            # Draw number using OpenCV (simpler)
            cv2.putText(numbered_result, str(i+1), (cx-20, cy+10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 6, cv2.LINE_AA)
            cv2.putText(numbered_result, str(i+1), (cx-20, cy+10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3, cv2.LINE_AA)
    
    visualizations['numbered'] = numbered_result
    
    # Calculate statistics
    total_field_area = sum(cv2.contourArea(cnt) for cnt in filtered_contours)
    total_image_area = original_width * original_height
    field_coverage = (total_field_area / total_image_area) * 100
    
    stats = {
        'num_fields': len(filtered_contours),
        'total_field_area': total_field_area,
        'field_coverage': field_coverage,
        'avg_field_size': total_field_area / len(filtered_contours) if len(filtered_contours) > 0 else 0
    }
    
    print(f"  Tarla kapsaması: {field_coverage:.1f}%")
    print(f"  Ortalama tarla boyutu: {stats['avg_field_size']:.0f} piksel²")
    
    return visualizations, stats

def create_field_visualization_with_model_output(original_image, model_output, threshold_map, binary_mask, visualizations, stats, output_path, image_name):
    """Create a 2x3 grid showing model output and field visualizations"""
    
    # Resize outputs to match original image size for display
    original_height, original_width = original_image.shape[:2]
    model_output_resized = cv2.resize(model_output, (original_width, original_height))
    threshold_map_resized = cv2.resize(threshold_map, (original_width, original_height))
    binary_mask_resized = cv2.resize(binary_mask, (original_width, original_height))
    
    # Create 2x3 grid
    fig, axes = plt.subplots(2, 3, figsize=(24, 16))
    
    # Row 1: Original, Model Output, Threshold Map
    axes[0, 0].imshow(original_image)
    axes[0, 0].set_title('Original Image', fontsize=14, fontweight='bold')
    axes[0, 0].axis('off')
    
    im1 = axes[0, 1].imshow(model_output_resized, cmap='gray', vmin=0, vmax=1)
    axes[0, 1].set_title(f'SAM-Adapter Model Output\nAvg Confidence: {model_output.mean():.3f}', fontsize=14, fontweight='bold')
    axes[0, 1].axis('off')
    plt.colorbar(im1, ax=axes[0, 1], fraction=0.046, pad=0.04)
    
    im2 = axes[0, 2].imshow(threshold_map_resized, cmap='viridis', vmin=0, vmax=1)
    axes[0, 2].set_title(f'Otsu 32x32 Threshold Map\nAvg Threshold: {threshold_map.mean():.3f}', fontsize=14, fontweight='bold')
    axes[0, 2].axis('off')
    plt.colorbar(im2, ax=axes[0, 2], fraction=0.046, pad=0.04)
    
    # Row 2: Binary Mask, Filled Areas, Contour Lines
    axes[1, 0].imshow(binary_mask_resized, cmap='gray', vmin=0, vmax=255)
    axes[1, 0].set_title(f'Binary Mask (Otsu 32x32)\nCoverage: {(binary_mask_resized > 127).sum() / binary_mask_resized.size * 100:.1f}%', 
                        fontsize=14, fontweight='bold')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(visualizations['filled'])
    axes[1, 1].set_title(f'Tarla Alanları (Renkli)\n{stats["num_fields"]} alan - %{stats["field_coverage"]:.1f} kapsama', 
                        fontsize=14, fontweight='bold')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(visualizations['numbered'])
    axes[1, 2].set_title(f'Numaralı Tarla Alanları\nOrtalama alan: {stats["avg_field_size"]:.0f} piksel²', 
                        fontsize=14, fontweight='bold')
    axes[1, 2].axis('off')
    
    plt.suptitle(f'SIMPLE OTSU 32x32 SLIDING WINDOW - {image_name}\nModel → Threshold → Binary → Field Areas', 
                 fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Model output dahil tarla görselleştirmesi kaydedildi: {output_path}")

def visualize_field_detection(rgb_image, binary_mask):
    """RGB görüntü üzerinde binary mask ile field detection görselleştirmesi oluşturur"""
    result = rgb_image.copy()
    
    # Binary mask'i 0-1 aralığına normalize et
    if binary_mask.max() > 1:
        mask = binary_mask / 255.0
    else:
        mask = binary_mask
    
    # Tespit edilen alanları yeşil renkte vurgula
    mask_colored = np.zeros_like(rgb_image)
    mask_colored[:, :, 1] = mask * 255  # Yeşil kanal
    
    # Alpha blending ile karıştır
    alpha = 0.4
    result = result.astype(np.float32)
    mask_colored = mask_colored.astype(np.float32)
    
    blended = (1 - alpha) * result + alpha * mask_colored
    blended = np.clip(blended, 0, 255).astype(np.uint8)
    
    return blended

def main():
    # Paths
    test_images_dir = 'test_images'
    output_dir = 'test_result/simple_otsu_with_model_output'
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load model
    print("SAM-Adapter modeli yükleniyor...")
    model = load_sam_adapter_model()
    print("Model başarıyla yüklendi!")
    
    # Get test images
    image_files = [f for f in os.listdir(test_images_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files.sort()
    
    print(f"\n{len(image_files)} test görüntüsü bulundu")
    print("🎯 SIMPLE OTSU 32x32 + MODEL OUTPUT VISUALİZATION")
    print("📊 Model çıktısı da dahil kapsamlı görselleştirme!")
    print("🌾 Model → Threshold → Binary → Field Areas pipeline!")
    print(f"Sonuçlar: {output_dir}")
    
    all_stats = []
    
    for i, image_file in enumerate(image_files):
        print(f"\n{'='*70}")
        print(f"İşleniyor ({i+1}/{len(image_files)}): {image_file}")
        print(f"{'='*70}")
        
        image_path = os.path.join(test_images_dir, image_file)
        output_path = os.path.join(output_dir, f"{os.path.splitext(image_file)[0]}_with_model_output.jpg")
        
        try:
            # Preprocess
            image_tensor, original_image, resized_image = preprocess_image(image_path)
            
            # Inference
            model_output = inference_sam_adapter(model, image_tensor)
            
            # Apply simple Otsu 32x32 sliding window (PROVEN METHOD)
            binary_mask, threshold_map = simple_otsu_sliding_window_32x32(model_output)
            
            # Draw field areas on original
            visualizations, stats = draw_field_areas_on_original(original_image, binary_mask, image_file)
            
            # Create comprehensive visualization with model output
            create_field_visualization_with_model_output(
                original_image, model_output, threshold_map, binary_mask, 
                visualizations, stats, output_path, image_file
            )
            
            # Add to overall stats
            stats['image'] = image_file
            stats['model_avg_confidence'] = model_output.mean()
            stats['threshold_avg'] = threshold_map.mean()
            all_stats.append(stats)
            
        except Exception as e:
            print(f"❌ HATA: {str(e)}")
            continue
    
    # Print overall summary
    print(f"\n{'='*80}")
    print("🎯 SIMPLE OTSU 32x32 + MODEL OUTPUT ÖZET")
    print(f"{'='*80}")
    print(f"Toplam işlenen görüntü: {len(all_stats)}")
    
    if all_stats:
        total_fields = sum(s['num_fields'] for s in all_stats)
        avg_coverage = np.mean([s['field_coverage'] for s in all_stats])
        avg_field_count = np.mean([s['num_fields'] for s in all_stats])
        avg_field_size = np.mean([s['avg_field_size'] for s in all_stats])
        avg_model_confidence = np.mean([s['model_avg_confidence'] for s in all_stats])
        avg_threshold = np.mean([s['threshold_avg'] for s in all_stats])
        
        print(f"Toplam tespit edilen tarla sayısı: {total_fields}")
        print(f"Ortalama görüntü başına tarla sayısı: {avg_field_count:.1f}")
        print(f"Ortalama tarla kapsaması: {avg_coverage:.1f}%")
        print(f"Ortalama tarla boyutu: {avg_field_size:.0f} piksel²")
        print(f"Ortalama model confidence: {avg_model_confidence:.3f}")
        print(f"Ortalama Otsu threshold: {avg_threshold:.3f}")
        
        # Best results
        most_fields = max(all_stats, key=lambda x: x['num_fields'])
        highest_coverage = max(all_stats, key=lambda x: x['field_coverage'])
        highest_confidence = max(all_stats, key=lambda x: x['model_avg_confidence'])
        
        print(f"\n🏆 En fazla tarla: {most_fields['image']} ({most_fields['num_fields']} tarla)")
        print(f"📊 En yüksek kapsama: {highest_coverage['image']} ({highest_coverage['field_coverage']:.1f}%)")
        print(f"🎯 En yüksek model confidence: {highest_confidence['image']} ({highest_confidence['model_avg_confidence']:.3f})")
    
    print(f"\n🎯 Sonuçlar: {output_dir}")
    print("✅ Kapsamlı görselleştirme içeriği:")
    print("  📊 SAM-Adapter model output (confidence haritası)")
    print("  🎯 Otsu 32x32 threshold map")
    print("  ⚫ Binary mask")
    print("  🌾 Renkli tarla alanları")
    print("  🔢 Numaralı tarla alanları")
    print("  📈 Model → Threshold → Binary → Field pipeline!")
    print("\n🚜 ARTIK MODEL ÇIKTISINI DA GÖREBİLİRSİNİZ!")

if __name__ == "__main__":
    main() 