# Binary Refinement Model

SAM-adapter'ın gray scale çıktılarını binary çıktılara dönüştürmek için geliştirilmiş refinement modeli.

## 🎯 Amaç

Bu model, SAM-adapter'ın ürettiği gray scale segmentasyon mask'lerini alıp, ground truth binary mask'lere uygun binary çıktılar üretir. Model threshold kullanmadan direkt olarak binary çıktı üretir.

## 📁 Proje <PERSON>pı<PERSON>ı

```
refinement/
├── model.py           # Model tanımları (SimpleBinaryRefinement, BinaryRefinementUNet)
├── dataset.py         # Dataset ve DataLoader sınıfları
├── train.py          # Training script'i
├── inference.py      # Inference script'i
├── requirements.txt  # Gerekli kütüphaneler
└── README.md        # Bu dosya
```

## 🚀 Kurulum

1. Gerekli kütüphaneleri yükleyin:
```bash
pip install -r requirements.txt
```

## 📊 Modeller

### 1. SimpleBinaryRefinement
- Hızl<PERSON> ve hafif model
- Düşük hesaplama maliyeti
- Gerçek zamanlı uygulamalar için ideal

### 2. BinaryRefinementUNet
- Daha kapsamlı U-Net tabanlı model
- Yüksek doğruluk
- Daha fazla hesaplama gücü gerektirir

## 🏋️ Training

### Temel Training
```bash
python train.py
```

### Özelleştirilmiş Training
```bash
python train.py \
    --sam_output_dir ../dataset_model_outputs/train_model_output \
    --ground_truth_dir ../dataset/train_mask \
    --model_type simple \
    --epochs 50 \
    --batch_size 8 \
    --lr 0.001 \
    --image_size 256
```

### Parametreler
- `--sam_output_dir`: SAM-adapter çıktılarının bulunduğu dizin
- `--ground_truth_dir`: Ground truth binary mask'lerin dizini
- `--model_type`: Model tipi (`simple` veya `unet`)
- `--epochs`: Epoch sayısı (varsayılan: 50)
- `--batch_size`: Batch boyutu (varsayılan: 8)
- `--lr`: Learning rate (varsayılan: 0.001)
- `--image_size`: Görüntü boyutu (varsayılan: 256)
- `--save_dir`: Model kaydetme dizini (varsayılan: `./checkpoints`)

## 🔮 Inference

### Tek Görüntü İşleme
```bash
python inference.py \
    --model_path ./checkpoints/best_model.pth \
    --single_image input.jpg \
    --output_dir ./outputs
```

### Dizin İşleme
```bash
python inference.py \
    --model_path ./checkpoints/best_model.pth \
    --input_dir ./sam_outputs \
    --output_dir ./binary_outputs
```

### SAM Çıktıları ile Karşılaştırma
```bash
python inference.py \
    --model_path ./checkpoints/best_model.pth \
    --sam_output_dir ../dataset_model_outputs/train_model_output \
    --ground_truth_dir ../dataset/train_mask \
    --output_dir ./comparison_results
```

## 📈 Monitoring

Training sırasında Tensorboard kullanabilirsiniz:

```bash
tensorboard --logdir runs/
```

## 🎛️ Model Özellikleri

### Loss Function
- **Binary Cross Entropy (BCE)**: Pixel-wise binary classification
- **Dice Loss**: Segmentasyon kalitesi için
- **Combined Loss**: BCE + Dice Loss

### Metrics
- **IoU (Intersection over Union)**: Segmentasyon doğruluğu
- **Training/Validation Loss**: Model performansı

### Optimizasyonlar
- **Adam Optimizer**: Adaptive learning rate
- **Learning Rate Scheduling**: ReduceLROnPlateau
- **Data Augmentation**: Horizontal/Vertical flip
- **Early Stopping**: En iyi modeli kaydetme

## 📋 Önerilen Training Ayarları

### Hızlı Test için
```bash
python train.py --epochs 20 --batch_size 16 --model_type simple
```

### Yüksek Doğruluk için
```bash
python train.py --epochs 100 --batch_size 4 --model_type unet --lr 0.0005
```

### GPU Memory Kısıtlı Ortamlar için
```bash
python train.py --batch_size 4 --image_size 128 --model_type simple
```

## 🔧 Troubleshooting

### Yaygın Sorunlar

1. **CUDA Out of Memory**
   - Batch size'ı azaltın (`--batch_size 4`)
   - Image size'ı küçültün (`--image_size 128`)

2. **Dataset Bulunamadı**
   - SAM output ve ground truth dizin yollarını kontrol edin
   - Dosya isimlerinin eşleştiğinden emin olun

3. **Model Yüklenmiyor**
   - Model tipinin doğru olduğunu kontrol edin
   - Model dosyasının doğru yolda olduğunu kontrol edin

### Performance Tips

1. **Hızlı Training için**
   - `SimpleBinaryRefinement` modelini kullanın
   - Batch size'ı artırın
   - Image size'ı küçültün

2. **Yüksek Doğruluk için**
   - `BinaryRefinementUNet` modelini kullanın
   - Daha fazla epoch kullanın
   - Data augmentation'ı artırın

## 📊 Beklenen Sonuçlar

- **Simple Model**: ~0.85+ IoU (5-10 epoch)
- **U-Net Model**: ~0.90+ IoU (15-25 epoch)
- **Training Süresi**: 
  - Simple: ~1-2 dakika/epoch (GPU)
  - U-Net: ~3-5 dakika/epoch (GPU)

## 🎯 Kullanım Senaryoları

1. **Real-time Processing**: SimpleBinaryRefinement + küçük image size
2. **High Accuracy**: BinaryRefinementUNet + büyük image size
3. **Batch Processing**: Inference script ile dizin işleme
4. **Quality Assessment**: Ground truth ile karşılaştırma modu 