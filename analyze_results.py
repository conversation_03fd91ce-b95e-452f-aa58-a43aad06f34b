#!/usr/bin/env python3
"""
Sonuçları analiz et ve orijinal ile karşılaştır
"""

import rasterio
import numpy as np
import matplotlib.pyplot as plt
import cv2
from skimage import filters, morphology, measure
from scipy import ndimage

def analyze_results():
    """Sonuçları detaylı analiz et"""
    
    # Orijinal dosyayı oku
    print("📖 Orijinal dosya okunuyor...")
    with rasterio.open("geotiff_input/asd_center_5k.tif") as src:
        original = src.read()
        if original.shape[0] <= 4:  # Channels first
            original = np.transpose(original, (1, 2, 0))
        if original.shape[2] == 1:
            original = original.squeeze(-1)
    
    print(f"📊 Orijinal shape: {original.shape}, dtype: {original.dtype}")
    print(f"📊 Orijinal range: {original.min()} - {original.max()}")
    
    # Model çıktısını oku
    print("📖 Model çıktısı okunuyor...")
    with rasterio.open("tiff_result/asd_center_5k_continuous.tif") as src:
        model_output = src.read(1)
    
    print(f"📊 Model output shape: {model_output.shape}, dtype: {model_output.dtype}")
    print(f"📊 Model output range: {model_output.min():.3f} - {model_output.max():.3f}")
    
    # Binary çıktısını oku
    print("📖 Binary çıktısı okunuyor...")
    with rasterio.open("tiff_result/asd_center_5k_binary.tif") as src:
        binary_output = src.read(1)
    
    print(f"📊 Binary shape: {binary_output.shape}, dtype: {binary_output.dtype}")
    print(f"📊 Binary range: {binary_output.min()} - {binary_output.max()}")
    print(f"📊 Binary pixels: {np.sum(binary_output > 127)} / {binary_output.size}")
    
    # Görselleştir
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Orijinal (RGB veya Grayscale)
    if len(original.shape) == 3:
        # RGB normalize et
        orig_vis = original.copy()
        if orig_vis.max() > 1:
            orig_vis = orig_vis / 255.0
        axes[0, 0].imshow(orig_vis)
    else:
        axes[0, 0].imshow(original, cmap='gray')
    axes[0, 0].set_title('Orijinal Görüntü')
    axes[0, 0].axis('off')
    
    # Model çıktısı
    axes[0, 1].imshow(model_output, cmap='viridis', vmin=0, vmax=1)
    axes[0, 1].set_title(f'Model Çıktısı\n(min={model_output.min():.3f}, max={model_output.max():.3f})')
    axes[0, 1].axis('off')
    
    # Binary çıktısı
    axes[0, 2].imshow(binary_output, cmap='gray')
    axes[0, 2].set_title(f'Binary Çıktısı\n({np.sum(binary_output > 127)} pixels)')
    axes[0, 2].axis('off')
    
    # Model çıktısı histogram
    axes[1, 0].hist(model_output.flatten(), bins=100, alpha=0.7)
    axes[1, 0].set_title('Model Çıktısı Histogram')
    axes[1, 0].set_xlabel('Değer')
    axes[1, 0].set_ylabel('Frekans')
    
    # Farklı threshold'lar dene
    thresholds = [0.3, 0.5, 0.7]
    for i, thresh in enumerate(thresholds):
        binary_test = (model_output > thresh).astype(np.uint8) * 255
        pixel_count = np.sum(binary_test > 127)
        
        if i == 0:
            axes[1, 1].imshow(binary_test, cmap='gray')
            axes[1, 1].set_title(f'Threshold {thresh}\n({pixel_count} pixels)')
            axes[1, 1].axis('off')
    
    # Otsu threshold
    otsu_thresh = filters.threshold_otsu(model_output)
    binary_otsu = (model_output > otsu_thresh).astype(np.uint8) * 255
    otsu_pixels = np.sum(binary_otsu > 127)
    
    axes[1, 2].imshow(binary_otsu, cmap='gray')
    axes[1, 2].set_title(f'Otsu Threshold ({otsu_thresh:.3f})\n({otsu_pixels} pixels)')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/analysis_comparison.png', dpi=150, bbox_inches='tight')
    print("💾 Analiz kaydedildi: tiff_result/analysis_comparison.png")
    plt.show()
    
    return original, model_output, binary_output

def improved_field_detection(model_output):
    """Geliştirilmiş tarla tespiti"""
    
    print("🔧 Geliştirilmiş tarla tespiti başlıyor...")
    
    # 1. Gaussian smoothing
    smoothed = cv2.GaussianBlur(model_output, (5, 5), 1.0)
    print(f"✅ Gaussian smoothing: {smoothed.min():.3f} - {smoothed.max():.3f}")
    
    # 2. Adaptive threshold
    # Model çıktısını 0-255 aralığına çevir
    model_8bit = (smoothed * 255).astype(np.uint8)
    
    # Adaptive threshold uygula
    adaptive_thresh = cv2.adaptiveThreshold(
        model_8bit, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
        cv2.THRESH_BINARY, 21, 10
    )
    print(f"✅ Adaptive threshold: {np.sum(adaptive_thresh > 127)} pixels")
    
    # 3. Morphological operations
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
    
    # Close small gaps
    closed = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel, iterations=2)
    print(f"✅ Morphological close: {np.sum(closed > 127)} pixels")
    
    # Remove small noise
    opened = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel, iterations=1)
    print(f"✅ Morphological open: {np.sum(opened > 127)} pixels")
    
    # 4. Connected components with area filtering
    num_labels, labels = cv2.connectedComponents(opened)
    
    # Area filtering
    min_area = 5000  # Minimum 5000 pixels
    filtered_labels = np.zeros_like(labels)
    label_counter = 1
    
    for label_id in range(1, num_labels):
        mask = (labels == label_id)
        area = np.sum(mask)
        
        if area >= min_area:
            filtered_labels[mask] = label_counter
            label_counter += 1
            print(f"   Tarla {label_counter-1}: {area} pixels")
    
    print(f"✅ {label_counter-1} büyük tarla alanı bulundu")
    
    # Görselleştir
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    axes[0, 0].imshow(model_output, cmap='viridis')
    axes[0, 0].set_title('Orijinal Model Çıktısı')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(smoothed, cmap='viridis')
    axes[0, 1].set_title('Gaussian Smoothed')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(adaptive_thresh, cmap='gray')
    axes[0, 2].set_title('Adaptive Threshold')
    axes[0, 2].axis('off')
    
    axes[1, 0].imshow(closed, cmap='gray')
    axes[1, 0].set_title('Morphological Close')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(opened, cmap='gray')
    axes[1, 1].set_title('Morphological Open')
    axes[1, 1].axis('off')
    
    # Colored fields
    colored_fields = np.zeros((filtered_labels.shape[0], filtered_labels.shape[1], 3), dtype=np.uint8)
    colors = plt.cm.tab10(np.linspace(0, 1, label_counter))[:, :3] * 255
    
    for label_id in range(1, label_counter):
        mask = (filtered_labels == label_id)
        colored_fields[mask] = colors[label_id-1]
    
    axes[1, 2].imshow(colored_fields)
    axes[1, 2].set_title(f'Improved Fields ({label_counter-1} fields)')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/improved_field_detection.png', dpi=150, bbox_inches='tight')
    print("💾 Geliştirilmiş tespit kaydedildi: tiff_result/improved_field_detection.png")
    plt.show()
    
    return filtered_labels, colored_fields

if __name__ == "__main__":
    # Mevcut sonuçları analiz et
    original, model_output, binary_output = analyze_results()
    
    # Geliştirilmiş tarla tespiti
    improved_labels, improved_colored = improved_field_detection(model_output)
    
    print("✅ Analiz tamamlandı!")
