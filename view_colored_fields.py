#!/usr/bin/env python3
"""
Renklendirilmiş tarla dosyasını görselleştir
"""

import rasterio
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import cv2

def view_colored_fields(tiff_path):
    """Renklendirilmiş tarla dosyasını görselleştir"""
    
    print(f"📖 Dosya okunuyor: {tiff_path}")
    
    with rasterio.open(tiff_path) as src:
        # RGB channels oku
        red = src.read(1)
        green = src.read(2) 
        blue = src.read(3)
        
        # RGB array oluştur
        rgb_array = np.stack([red, green, blue], axis=-1)
        
        print(f"📊 Görüntü boyutu: {rgb_array.shape}")
        print(f"📊 Veri tipi: {rgb_array.dtype}")
        print(f"📊 Değer aralığı: {rgb_array.min()} - {rgb_array.max()}")
        
        # Unique colors bul
        unique_colors = []
        for i in range(rgb_array.shape[0]):
            for j in range(rgb_array.shape[1]):
                color = tuple(rgb_array[i, j])
                if color not in unique_colors and color != (0, 0, 0):
                    unique_colors.append(color)
        
        print(f"🎨 Bulunan renk sayısı: {len(unique_colors)}")
        print(f"🎨 Renkler: {unique_colors[:10]}...")  # İlk 10 renk
        
        # Görselleştir
        plt.figure(figsize=(15, 15))
        
        # Ana görüntü
        plt.subplot(2, 2, 1)
        plt.imshow(rgb_array)
        plt.title(f'Renklendirilmiş Tarlalar ({len(unique_colors)} renk)')
        plt.axis('off')
        
        # Red channel
        plt.subplot(2, 2, 2)
        plt.imshow(red, cmap='Reds')
        plt.title('Red Channel')
        plt.axis('off')
        
        # Green channel
        plt.subplot(2, 2, 3)
        plt.imshow(green, cmap='Greens')
        plt.title('Green Channel')
        plt.axis('off')
        
        # Blue channel
        plt.subplot(2, 2, 4)
        plt.imshow(blue, cmap='Blues')
        plt.title('Blue Channel')
        plt.axis('off')
        
        plt.tight_layout()
        
        # Kaydet
        output_path = tiff_path.replace('.tif', '_visualization.png')
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"💾 Görselleştirme kaydedildi: {output_path}")
        
        plt.show()
        
        return rgb_array, unique_colors

def view_field_segments(segments_path):
    """Tarla segmentlerini görselleştir"""
    
    print(f"📖 Segment dosyası okunuyor: {segments_path}")
    
    with rasterio.open(segments_path) as src:
        segments = src.read(1)
        
        print(f"📊 Segment boyutu: {segments.shape}")
        print(f"📊 Segment sayısı: {np.max(segments)}")
        print(f"📊 Unique segments: {len(np.unique(segments))}")
        
        # Colormap oluştur
        n_segments = int(np.max(segments)) + 1
        colors = plt.cm.tab20(np.linspace(0, 1, n_segments))
        cmap = ListedColormap(colors)
        
        plt.figure(figsize=(12, 12))
        plt.imshow(segments, cmap=cmap)
        plt.title(f'Tarla Segmentleri ({n_segments} segment)')
        plt.colorbar(label='Segment ID')
        
        # Kaydet
        output_path = segments_path.replace('.tif', '_visualization.png')
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"💾 Segment görselleştirmesi kaydedildi: {output_path}")
        
        plt.show()
        
        return segments

if __name__ == "__main__":
    # Renklendirilmiş tarlalar
    colored_path = "tiff_result/asd_center_5k_colored_fields.tif"
    rgb_array, colors = view_colored_fields(colored_path)
    
    # Tarla segmentleri
    segments_path = "tiff_result/asd_center_5k_field_segments.tif"
    segments = view_field_segments(segments_path)
    
    print("✅ Görselleştirme tamamlandı!")
