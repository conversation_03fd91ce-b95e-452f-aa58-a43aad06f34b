import os
import torch
import numpy as np
import cv2
import yaml
from torchvision import transforms
import rasterio
from rasterio.windows import Window
import warnings
warnings.filterwarnings('ignore')
import time
from sklearn.cluster import KMeans

# SAM-Adapter imports
from models import make as model_make
from models import sam  # SAM model register edilsin

# Simple otsu görselleştirme fonksiyonlarını içe aktar
from simple_otsu_direct_with_model_output import (
    simple_otsu_sliding_window_32x32,
    visualize_field_detection,
)

class GeotiffProcessor:
    """Geotiff (.tif, .tiff), ESW (.esw) ve ECW (.ecw) dosyalarını SAM-Adapter ile işleyen sınıf"""

    def __init__(self, config_path='configs/cod-sam-vit-h.yaml',
                 checkpoint_path='save/checkpoint_14.pth',
                 num_colors=24):
        self.config_path = config_path
        self.checkpoint_path = checkpoint_path
        self.model = None
        # Inference pencere boyutu ve padding
        self.infer_window = 512  # Model inference için 512x512 patch
        self.window_size = self.infer_window  # Eski değişkenle uyumlu
        self.padding_size = self.infer_window // 2  # 256 px padding
        self.output_dir = 'tiff_result'

        # Renklendirme parametreleri
        self.num_colors = num_colors  # Kullanılacak renk sayısı
        self.blend_sigma = 32  # Gaussian blending için sigma değeri
        self.overlap_blend_width = 128  # Overlap bölgelerinde blending genişliği

        # Çıktı klasörünü oluştur
        os.makedirs(self.output_dir, exist_ok=True)

        print("🚀 GeotiffProcessor başlatılıyor...")
        print(f"🎨 Renk sayısı: {self.num_colors}")
        self._check_gpu_status()
        self._load_model()

    def _check_gpu_status(self):
        """GPU durumunu kontrol et ve raporla"""
        print("🔍 GPU Status Kontrolü:")

        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_device)

            print(f"✅ CUDA Available: {gpu_count} GPU(s)")
            print(f"🎯 Current GPU: {current_device} - {gpu_name}")

            # GPU memory info
            total_memory = torch.cuda.get_device_properties(current_device).total_memory
            allocated_memory = torch.cuda.memory_allocated(current_device)
            cached_memory = torch.cuda.memory_reserved(current_device)

            print(f"💾 GPU Memory:")
            print(f"   Total: {total_memory / 1024**3:.1f} GB")
            print(f"   Allocated: {allocated_memory / 1024**3:.1f} GB")
            print(f"   Cached: {cached_memory / 1024**3:.1f} GB")
            print(f"   Free: {(total_memory - allocated_memory) / 1024**3:.1f} GB")
        else:
            print("❌ CUDA Not Available - CPU mode")
            print("⚠️ Bu çok yavaş olacak!")

        print("-" * 50)

    def _load_model(self):
        """SAM-Adapter modelini yükle"""
        print("📦 Model yükleniyor...")
        
        # Config yükle
        with open(self.config_path, 'r') as f:
            config = yaml.load(f, Loader=yaml.FullLoader)
        
        # Model oluştur
        self.model = model_make(config['model']).cuda()
        
        # Checkpoint yükle
        checkpoint = torch.load(self.checkpoint_path, map_location='cpu')
        
        # Model state dict yükle
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'], strict=True)
            print(f"✅ Model epoch {checkpoint.get('epoch', 'unknown')}'dan yüklendi")
        else:
            self.model.load_state_dict(checkpoint, strict=True)
            print("✅ Model başarıyla yüklendi")
        
        # Eval moduna al
        self.model.eval()
        
    def _preprocess_patch(self, patch):
        """Patch'i model için ön işleme"""
        
        # RGB formatına çevir (geotiff genelde RGB veya grayscale olabilir)
        if len(patch.shape) == 2:  # Grayscale
            patch = np.stack([patch, patch, patch], axis=-1)
        elif patch.shape[2] == 4:  # RGBA
            patch = patch[:, :, :3]  # Alpha kanalını at
            
        # 0-255 aralığında normalize et
        if patch.max() <= 1.0:
            patch = (patch * 255).astype(np.uint8)
        
        # Patch zaten 1024x1024 olmalı (tile processing'den geliyor)
        # Eğer değilse resize et
        if patch.shape[0] != 1024 or patch.shape[1] != 1024:
            patch_resized = cv2.resize(patch, (1024, 1024))
        else:
            patch_resized = patch
        
        # Tensor'a çevir ve normalize et
        transform = transforms.Compose([
            transforms.ToTensor(),
        ])
        
        patch_tensor = transform(patch_resized).unsqueeze(0).cuda()
        # SAM normalizasyonu: [-1, 1]
        normalized_patch = (patch_tensor - 0.5) / 0.5
        
        return normalized_patch
        
    def _inference_patch(self, patch_tensor):
        """Patch için model inference - GPU monitored"""
        with torch.no_grad():
            # GPU memory before inference
            if torch.cuda.is_available():
                mem_before = torch.cuda.memory_allocated() / 1024**3

            # SAM model için input set et
            self.model.set_input(patch_tensor, patch_tensor)  # dummy gt for inference

            # Forward pass
            self.model.forward()

            # Prediction al
            prediction = self.model.pred_mask

            # GPU memory after inference
            if torch.cuda.is_available():
                mem_after = torch.cuda.memory_allocated() / 1024**3
                mem_used = mem_after - mem_before
                if mem_used > 0.1:  # 100MB'dan fazlaysa raporla
                    print(f"🔥 GPU Memory used for inference: {mem_used:.2f} GB")

            # Tensor boyutlarını düzenle
            if prediction.dim() == 4:
                prediction = prediction.squeeze(0).squeeze(0)
            elif prediction.dim() == 3:
                prediction = prediction.squeeze(0)

            # Sigmoid uygula
            prediction = torch.sigmoid(prediction)
            pred_np = prediction.cpu().numpy()

            return pred_np
    
    def _calculate_local_otsu_threshold(self, window):
        """Pencere için Otsu threshold hesapla"""
        if window.size == 0 or window.std() == 0:
            return 0.5
        
        # 8-bit'e çevir
        window_8bit = (window * 255).astype(np.uint8)
        
        try:
            threshold_val, _ = cv2.threshold(window_8bit, 0, 255, 
                                           cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            return threshold_val / 255.0
        except:
            return 0.5
    
    def _apply_simple_otsu_direct(self, prediction, window_size=1024, step_size=512):
        """Otsu kaydırmalı pencere yöntemi (parametrik). Varsayılan 1024x1024 pencere, 512 adım."""
        print(f"🎯 Otsu Sliding Window uygulanıyor (window={window_size}, step={step_size})...")
        
        height, width = prediction.shape
        
        threshold_map = np.zeros_like(prediction)
        weight_map = np.zeros_like(prediction)
        
        # Sliding window
        for y in range(0, height - window_size + 1, step_size):
            for x in range(0, width - window_size + 1, step_size):
                window = prediction[y:y+window_size, x:x+window_size]
                
                # Otsu threshold hesapla
                local_threshold = self._calculate_local_otsu_threshold(window)
                
                threshold_map[y:y+window_size, x:x+window_size] += local_threshold
                weight_map[y:y+window_size, x:x+window_size] += 1
        
        # Kapsanmayan bölgeleri işle
        uncovered_mask = weight_map == 0
        if uncovered_mask.any():
            global_threshold = self._calculate_local_otsu_threshold(prediction)
            threshold_map[uncovered_mask] = global_threshold
            weight_map[uncovered_mask] = 1
        
        # Overlap bölgelerini ortala
        threshold_map = threshold_map / weight_map
        
        # Threshold uygula
        binary_mask = (prediction > threshold_map).astype(np.uint8) * 255
        
        # Morphological temizlik
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel, iterations=1)
        binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        return binary_mask, threshold_map
    
    def _create_padded_windows(self, geotiff_data, offset_top_left=(0, 0)):
        """Geotiff data'dan 1024x1024 pencereler oluştur (padding mevcut). 
        offset_top_left=(dy,dx) orijinal görüntünün hangi noktadan itibaren pencere
        kaydırmasının başlayacağını belirtir. (0,0) → pad'in başlangıcı,
        (512,512) → pad'den sonra tam orijinal başlangıç.
        Geriye: windows list, positions list (orijinal koordinatlar), original_shape
        """

        height, width = geotiff_data.shape[:2]

        # Padding ekle
        if len(geotiff_data.shape) == 3:
            padded_data = np.pad(
                geotiff_data,
                ((self.padding_size, self.padding_size),
                 (self.padding_size, self.padding_size),
                 (0, 0)),
                mode='reflect'
            )
        else:
            padded_data = np.pad(
                geotiff_data,
                ((self.padding_size, self.padding_size),
                 (self.padding_size, self.padding_size)),
                mode='reflect'
            )

        dy_off, dx_off = offset_top_left  # orijinal eksende kaydırma

        # Padded eksende başlangıç
        start_y = self.padding_size - dy_off
        start_x = self.padding_size - dx_off

        windows = []
        positions = []

        step = self.window_size  # 1024

        y = start_y
        while y + self.window_size <= padded_data.shape[0]:
            x = start_x
            while x + self.window_size <= padded_data.shape[1]:
                window = padded_data[y:y + self.window_size, x:x + self.window_size]
                # orijinal koordinat
                orig_y = y - self.padding_size
                orig_x = x - self.padding_size
                windows.append(window)
                positions.append((orig_y, orig_x))
                x += step
            y += step

        return windows, positions, (height, width)
    
    def _reconstruct_from_windows(self, processed_windows, positions, original_shape):
        """İşlenmiş pencerelerden orijinal boyutta görüntü oluştur"""
        result = np.zeros(original_shape[:2], dtype=np.float32)
        weight_map = np.zeros(original_shape[:2], dtype=np.float32)
        
        for prediction, (y_start, x_start, y_end, x_end) in zip(processed_windows, positions):
            result[y_start:y_end, x_start:x_end] += prediction
            weight_map[y_start:y_end, x_start:x_end] += 1
        
        # Ortalama al (overlap olan bölgeler için)
        weight_map[weight_map == 0] = 1
        result = result / weight_map
        
        return result

    def _create_gaussian_blend_weights(self, height, width, overlap_width):
        """Gaussian blending için ağırlık haritası oluştur"""
        weights = np.ones((height, width), dtype=np.float32)

        # Kenarlardan overlap_width kadar içeride gaussian falloff uygula
        for i in range(overlap_width):
            # Gaussian weight hesapla (0'dan 1'e)
            weight = np.exp(-0.5 * ((i - overlap_width) / (overlap_width / 3)) ** 2)

            # Kenarları yumuşat
            weights[i, :] *= weight  # Üst kenar
            weights[-(i+1), :] *= weight  # Alt kenar
            weights[:, i] *= weight  # Sol kenar
            weights[:, -(i+1)] *= weight  # Sağ kenar

        return weights

    def _seamless_blend_predictions(self, predictions, positions, original_shape, overlap_width=128):
        """Prediction'ları seamless blending ile birleştir"""
        result = np.zeros(original_shape[:2], dtype=np.float32)
        weight_map = np.zeros(original_shape[:2], dtype=np.float32)

        for prediction, (y_start, x_start, y_end, x_end) in zip(predictions, positions):
            pred_height, pred_width = prediction.shape

            # Bu prediction için gaussian weights oluştur
            blend_weights = self._create_gaussian_blend_weights(pred_height, pred_width, overlap_width)

            # Weighted prediction ekle
            result[y_start:y_end, x_start:x_end] += prediction * blend_weights
            weight_map[y_start:y_end, x_start:x_end] += blend_weights

        # Normalize et
        weight_map[weight_map == 0] = 1
        result = result / weight_map

        return result

    def _extract_field_segments(self, binary_mask):
        """Binary mask'ten tarla segmentlerini çıkart"""
        # Morphological operations ile noise temizle
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        cleaned_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_OPEN, kernel, iterations=1)

        # Connected components ile tarla alanlarını bul
        num_labels, labels = cv2.connectedComponents(cleaned_mask.astype(np.uint8))

        # Küçük alanları filtrele (minimum alan threshold)
        min_area = 1000  # piksel cinsinden minimum alan
        filtered_labels = np.zeros_like(labels)
        label_counter = 1

        for label_id in range(1, num_labels):
            mask = (labels == label_id)
            area = np.sum(mask)

            if area >= min_area:
                filtered_labels[mask] = label_counter
                label_counter += 1

        return filtered_labels, label_counter - 1

    def _cluster_fields_by_proximity(self, field_labels, num_fields, target_colors):
        """Tarla alanlarını yakınlığa göre kümeleyerek renklendirme"""
        if num_fields == 0:
            return field_labels

        # Her tarla alanının merkez koordinatını hesapla
        field_centers = []
        valid_labels = []

        for label_id in range(1, num_fields + 1):
            mask = (field_labels == label_id)
            if np.sum(mask) > 0:
                y_coords, x_coords = np.where(mask)
                center_y = np.mean(y_coords)
                center_x = np.mean(x_coords)
                field_centers.append([center_y, center_x])
                valid_labels.append(label_id)

        if len(field_centers) == 0:
            return field_labels

        field_centers = np.array(field_centers)

        # K-means clustering ile yakın tarlaları grupla
        n_clusters = min(target_colors, len(field_centers))
        if n_clusters <= 1:
            return field_labels

        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(field_centers)

        # Yeni label map oluştur
        clustered_labels = np.zeros_like(field_labels)

        for i, original_label in enumerate(valid_labels):
            cluster_id = cluster_labels[i] + 1  # 0'dan kaçın
            mask = (field_labels == original_label)
            clustered_labels[mask] = cluster_id

        return clustered_labels

    def _create_colored_field_map(self, clustered_labels, num_clusters):
        """Kümelenmiş tarla alanlarını renklendir"""
        # Distinctive color palette oluştur
        colors = self._generate_distinctive_colors(num_clusters)

        height, width = clustered_labels.shape
        colored_map = np.zeros((height, width, 3), dtype=np.uint8)

        for cluster_id in range(1, num_clusters + 1):
            mask = (clustered_labels == cluster_id)
            if np.sum(mask) > 0:
                color = colors[cluster_id - 1]
                colored_map[mask] = color

        return colored_map

    def _generate_distinctive_colors(self, num_colors):
        """Birbirinden ayırt edilebilir renkler oluştur"""
        if num_colors <= 0:
            return []

        # HSV color space'de eşit aralıklarla renkler oluştur
        colors = []
        for i in range(num_colors):
            hue = (i * 360 / num_colors) % 360
            saturation = 0.7 + (i % 3) * 0.1  # 0.7-1.0 arası
            value = 0.8 + (i % 2) * 0.2       # 0.8-1.0 arası

            # HSV'den RGB'ye çevir
            hsv_color = np.array([[[hue, saturation * 255, value * 255]]], dtype=np.uint8)
            rgb_color = cv2.cvtColor(hsv_color, cv2.COLOR_HSV2RGB)[0, 0]
            colors.append(rgb_color)

        return colors

    def _create_threshold_map(self, model_output, binary_result):
        """Model çıktısı ve binary sonuç arasında threshold map oluştur"""
        # Binary mask'i 0-1 aralığına normalize et
        if binary_result.max() > 1:
            binary_normalized = binary_result / 255.0
        else:
            binary_normalized = binary_result
        
        # Threshold map: model çıktısı ile binary sonuç arasındaki fark
        threshold_map = np.abs(model_output - binary_normalized)
        
        # 0-1 aralığında normalize et
        if threshold_map.max() > 0:
            threshold_map = threshold_map / threshold_map.max()
        
        return threshold_map
    
    def process_geotiff(self, geotiff_path):
        """Ana raster dosya işleme fonksiyonu (Geotiff/ESW)"""
        file_ext = os.path.splitext(geotiff_path)[1].lower()
        if file_ext in ['.tif', '.tiff']:
            file_type = "Geotiff"
        elif file_ext == '.esw':
            file_type = "ESW"
        elif file_ext == '.ecw':
            file_type = "ECW"
        else:
            file_type = "Raster"
        
        print(f"🌍 {file_type} dosyası işleniyor: {geotiff_path}")
        
        # Dosya formatına göre okuma yöntemi seç
        if file_ext == '.ecw':
            # ECW dosyaları için çoklu yöntem denemesi
            print("🔍 ECW dosyası tespit edildi - alternatif okuma yöntemleri deneniyor...")
            
            # Yöntem 1: GDAL subprocess ile
            try:
                print("📝 Yöntem 1: GDAL komut satırı ile GeoTIFF'e dönüştürme...")
                import subprocess
                import tempfile
                
                # Geçici GeoTIFF dosyası oluştur
                temp_tiff = os.path.join("temp_ecw_converted.tif")
                
                # GDAL translate komutu
                cmd = [
                    'gdal_translate',
                    '-of', 'GTiff',
                    '-co', 'COMPRESS=LZW',
                    '-co', 'TILED=YES',
                    geotiff_path,
                    temp_tiff
                ]
                
                print(f"🔧 Çalıştırılıyor: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0 and os.path.exists(temp_tiff):
                    print(f"✅ ECW başarıyla GeoTIFF'e dönüştürüldü: {temp_tiff}")
                    # Dönüştürülen dosyayı işle
                    with rasterio.open(temp_tiff) as src:
                        height, width = src.height, src.width
                        file_size_gb = (height * width * src.count * 1) / (1024**3)
                        
                        print(f"📊 Dönüştürülen raster boyutu: {height} x {width} (≈{file_size_gb:.1f} GB)")
                        
                        profile = src.profile
                        transform = src.transform
                        crs = src.crs
                        
                        # İşlem sonunda temp dosyayı sil
                        result = self._process_streaming_geotiff(src, profile, transform, crs, geotiff_path)
                        
                        # Cleanup
                        try:
                            os.remove(temp_tiff)
                            print(f"🗑️ Geçici dosya silindi: {temp_tiff}")
                        except:
                            pass
                        
                        return result
                else:
                    raise Exception(f"GDAL dönüştürme başarısız: {result.stderr}")
                    
            except Exception as e:
                print(f"⚠️ Yöntem 1 başarısız: {e}")
                
                # Yöntem 2: PIL/Pillow ile deneme
                try:
                    print("📝 Yöntem 2: PIL/Pillow ile okuma...")
                    from PIL import Image
                    
                    # PIL ile ECW okuma denemesi
                    img = Image.open(geotiff_path)
                    img_array = np.array(img)
                    
                    print(f"✅ PIL ile ECW okundu: {img_array.shape}")
                    
                    # Basit metadata oluştur
                    height, width = img_array.shape[:2]
                    channels = 1 if len(img_array.shape) == 2 else img_array.shape[2]
                    
                    profile = {
                        'driver': 'GTiff',
                        'dtype': str(img_array.dtype),
                        'nodata': None,
                        'width': width,
                        'height': height,
                        'count': channels,
                        'crs': None,
                        'transform': None,
                        'compress': 'lzw'
                    }
                    
                    # Basit transform (piksel koordinatları)
                    from rasterio.transform import from_bounds
                    transform = from_bounds(0, 0, width, height, width, height)
                    crs = None
                    
                    print(f"📊 PIL raster boyutu: {height} x {width} x {channels}")
                    print("⚠️ Koordinat sistemi bilgisi yok - piksel koordinatları kullanılıyor")
                    
                    # Geçici rasterio-compatible dataset oluştur
                    import tempfile
                    temp_tiff = "temp_pil_ecw.tif"
                    
                    with rasterio.open(temp_tiff, 'w', **profile) as dst:
                        if len(img_array.shape) == 2:
                            dst.write(img_array, 1)
                        else:
                            for i in range(channels):
                                dst.write(img_array[:,:,i], i+1)
                    
                    # Streaming processing
                    with rasterio.open(temp_tiff) as src:
                        result = self._process_streaming_geotiff(src, profile, transform, crs, geotiff_path)
                        
                        # Cleanup
                        try:
                            os.remove(temp_tiff)
                        except:
                            pass
                        
                        return result
                        
                except Exception as e2:
                    print(f"⚠️ Yöntem 2 başarısız: {e2}")
                    
                    # Yöntem 3: OpenCV ile deneme
                    try:
                        print("📝 Yöntem 3: OpenCV ile okuma...")
                        
                        img_array = cv2.imread(geotiff_path, cv2.IMREAD_UNCHANGED)
                        if img_array is None:
                            raise Exception("OpenCV ile okuma başarısız")
                        
                        # OpenCV BGR -> RGB
                        if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                            img_array = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)
                        
                        print(f"✅ OpenCV ile ECW okundu: {img_array.shape}")
                        
                        # Basit metadata oluştur
                        height, width = img_array.shape[:2]
                        channels = 1 if len(img_array.shape) == 2 else img_array.shape[2]
                        
                        profile = {
                            'driver': 'GTiff',
                            'dtype': str(img_array.dtype),
                            'nodata': None,
                            'width': width,
                            'height': height,
                            'count': channels,
                            'crs': None,
                            'transform': None,
                            'compress': 'lzw'
                        }
                        
                        # Basit transform
                        from rasterio.transform import from_bounds
                        transform = from_bounds(0, 0, width, height, width, height)
                        crs = None
                        
                        print(f"📊 OpenCV raster boyutu: {height} x {width} x {channels}")
                        print("⚠️ Koordinat sistemi bilgisi yok - piksel koordinatları kullanılıyor")
                        
                        # Geçici rasterio-compatible dataset oluştur
                        temp_tiff = "temp_opencv_ecw.tif"
                        
                        with rasterio.open(temp_tiff, 'w', **profile) as dst:
                            if len(img_array.shape) == 2:
                                dst.write(img_array, 1)
                            else:
                                for i in range(channels):
                                    dst.write(img_array[:,:,i], i+1)
                        
                        # Streaming processing
                        with rasterio.open(temp_tiff) as src:
                            result = self._process_streaming_geotiff(src, profile, transform, crs, geotiff_path)
                            
                            # Cleanup
                            try:
                                os.remove(temp_tiff)
                            except:
                                pass
                            
                            return result
                            
                    except Exception as e3:
                        print(f"⚠️ Yöntem 3 başarısız: {e3}")
                        
                        # Yöntem 4: imageio ile deneme
                        try:
                            print("📝 Yöntem 4: imageio ile okuma...")
                            import imageio
                            
                            img_array = imageio.imread(geotiff_path)
                            print(f"✅ imageio ile ECW okundu: {img_array.shape}")
                            
                            # Basit metadata oluştur
                            height, width = img_array.shape[:2]
                            channels = 1 if len(img_array.shape) == 2 else img_array.shape[2]
                            
                            profile = {
                                'driver': 'GTiff',
                                'dtype': str(img_array.dtype),
                                'nodata': None,
                                'width': width,
                                'height': height,
                                'count': channels,
                                'crs': None,
                                'transform': None,
                                'compress': 'lzw'
                            }
                            
                            # Basit transform
                            from rasterio.transform import from_bounds
                            transform = from_bounds(0, 0, width, height, width, height)
                            crs = None
                            
                            print(f"📊 imageio raster boyutu: {height} x {width} x {channels}")
                            print("⚠️ Koordinat sistemi bilgisi yok - piksel koordinatları kullanılıyor")
                            
                            # Geçici rasterio-compatible dataset oluştur
                            temp_tiff = "temp_imageio_ecw.tif"
                            
                            with rasterio.open(temp_tiff, 'w', **profile) as dst:
                                if len(img_array.shape) == 2:
                                    dst.write(img_array, 1)
                                else:
                                    for i in range(channels):
                                        dst.write(img_array[:,:,i], i+1)
                            
                            # Streaming processing
                            with rasterio.open(temp_tiff) as src:
                                result = self._process_streaming_geotiff(src, profile, transform, crs, geotiff_path)
                                
                                # Cleanup
                                try:
                                    os.remove(temp_tiff)
                                except:
                                    pass
                                
                                return result
                                
                        except Exception as e4:
                            print(f"⚠️ Yöntem 4 başarısız: {e4}")
                            
                            # Yöntem 5: skimage ile deneme
                            try:
                                print("📝 Yöntem 5: scikit-image ile okuma...")
                                from skimage import io
                                
                                img_array = io.imread(geotiff_path)
                                print(f"✅ skimage ile ECW okundu: {img_array.shape}")
                                
                                # Basit metadata oluştur
                                height, width = img_array.shape[:2]
                                channels = 1 if len(img_array.shape) == 2 else img_array.shape[2]
                                
                                profile = {
                                    'driver': 'GTiff',
                                    'dtype': str(img_array.dtype),
                                    'nodata': None,
                                    'width': width,
                                    'height': height,
                                    'count': channels,
                                    'crs': None,
                                    'transform': None,
                                    'compress': 'lzw'
                                }
                                
                                # Basit transform
                                from rasterio.transform import from_bounds
                                transform = from_bounds(0, 0, width, height, width, height)
                                crs = None
                                
                                print(f"📊 skimage raster boyutu: {height} x {width} x {channels}")
                                print("⚠️ Koordinat sistemi bilgisi yok - piksel koordinatları kullanılıyor")
                                
                                # Geçici rasterio-compatible dataset oluştur
                                temp_tiff = "temp_skimage_ecw.tif"
                                
                                with rasterio.open(temp_tiff, 'w', **profile) as dst:
                                    if len(img_array.shape) == 2:
                                        dst.write(img_array, 1)
                                    else:
                                        for i in range(channels):
                                            dst.write(img_array[:,:,i], i+1)
                                
                                # Streaming processing
                                with rasterio.open(temp_tiff) as src:
                                    result = self._process_streaming_geotiff(src, profile, transform, crs, geotiff_path)
                                    
                                    # Cleanup
                                    try:
                                        os.remove(temp_tiff)
                                    except:
                                        pass
                                    
                                    return result
                                    
                            except Exception as e5:
                                print(f"⚠️ Yöntem 5 başarısız: {e5}")
                                
                                # Yöntem 6: Raw binary okuma denemesi
                                try:
                                    print("📝 Yöntem 6: Ham binary okuma...")
                                    
                                    # Dosya boyutunu kontrol et
                                    file_size = os.path.getsize(geotiff_path)
                                    print(f"📊 Dosya boyutu: {file_size / (1024*1024):.1f} MB")
                                    
                                    # Çok büyükse atlama
                                    if file_size > 100 * 1024 * 1024:  # 100MB'dan büyükse
                                        raise Exception("Dosya çok büyük - ham okuma atlandı")
                                    
                                    # Binary okuma
                                    with open(geotiff_path, 'rb') as f:
                                        # ECW header kontrolü
                                        header = f.read(16)
                                        if b'ECW' not in header and b'ecw' not in header:
                                            raise Exception("ECW header bulunamadı")
                                        
                                        print("✅ ECW dosyası tespit edildi ancak binary decode edilemiyor")
                                        raise Exception("Binary decode desteklenmiyor")
                                        
                                except Exception as e6:
                                    print(f"⚠️ Yöntem 6 başarısız: {e6}")
                                    
                                    # Tüm yöntemler başarısız
                                    print(f"❌ Tüm ECW okuma yöntemleri başarısız (6 yöntem denendi)")
                
                print("\n💡 ECW Dosya Desteği:")
                print("   ECW formatı özel bir sıkıştırma formatıdır ve özel driver gerektirir.")
                print("   Detaylı dönüştürme kılavuzu için:")
                print(f"   python ecw_converter_guide.py --input {geotiff_path}")
                print("\n📌 Hızlı çözümler:")
                print("   1. QGIS ile dosyayı GeoTIFF (.tif) formatına dönüştürün")
                print("   2. Online dönüştürücü: https://mygeodata.cloud/converter/")
                print("\n🚀 Dönüştürme kılavuzunu çalıştırıyor...")
                
                # ECW dönüştürme kılavuzunu otomatik çalıştır
                try:
                    import subprocess
                    subprocess.run([
                        'python', 'ecw_converter_guide.py', 
                        '--input', geotiff_path
                    ], check=False)
                except Exception as e:
                    print(f"⚠️ Kılavuz çalıştırılamadı: {e}")
                    print("   Manuel çalıştırın: python ecw_converter_guide.py --input your_file.ecw")
        else:
            # Rasterio ile dosya bilgilerini al (veri yüklemeden)
            try:
                with rasterio.open(geotiff_path) as src:
                    # Dosya boyutunu kontrol et
                    height, width = src.height, src.width
                    file_size_gb = (height * width * src.count * 1) / (1024**3)  # uint8 varsayımı
                    
                    print(f"📊 Raster boyutu: {height} x {width} (≈{file_size_gb:.1f} GB)")
                    
                    # Metadata'yı sakla
                    profile = src.profile
                    transform = src.transform
                    crs = src.crs
                    
                    # HER DOSYA için streaming-based processing (bellek sınırlaması yok)
                    print("⚡ Streaming-based processing (büyük dosya optimizasyonu)")
                    return self._process_streaming_geotiff(src, profile, transform, crs, geotiff_path)
                    
            except Exception as e:
                print(f"❌ Dosya okunamadı: {e}")
                return None
    
    def _process_streaming_geotiff(self, src, profile, transform, crs, geotiff_path):
        """Streaming-based processing - RAM'e sığmayan dosyalar için"""
        print("🌊 Streaming-based processing başlatılıyor...")
        
        height, width = src.height, src.width
        
        # Çıktı dosyaları için hazırlık
        base_name = os.path.splitext(os.path.basename(geotiff_path))[0]
        output_dir = "tiff_result"
        os.makedirs(output_dir, exist_ok=True)
        
        # Çıktı dosya yolları
        continuous_path = os.path.join(output_dir, f"{base_name}_continuous.tif")
        binary_path = os.path.join(output_dir, f"{base_name}_binary.tif")
        threshold_path = os.path.join(output_dir, f"{base_name}_threshold.tif")
        field_segments_path = os.path.join(output_dir, f"{base_name}_field_segments.tif")
        colored_fields_path = os.path.join(output_dir, f"{base_name}_colored_fields.tif")
        visualization_path = os.path.join(output_dir, f"{base_name}_visualization.png")
        
        # Çıktı profilleri
        output_profile = profile.copy()
        output_profile.update({
            'count': 1,
            'dtype': 'float32',
            'compress': 'lzw',
            'tiled': True,
            'blockxsize': 1024,
            'blockysize': 1024,
            'BIGTIFF': 'YES'  # Büyük dosyalar için BigTIFF
        })
        
        # Çıktı dosyalarını oluştur
        continuous_dst = rasterio.open(continuous_path, 'w', **output_profile)

        binary_profile = output_profile.copy()
        binary_profile['dtype'] = 'uint8'
        binary_dst = rasterio.open(binary_path, 'w', **binary_profile)

        threshold_dst = rasterio.open(threshold_path, 'w', **output_profile)

        # Field segments için dosya
        segments_profile = output_profile.copy()
        segments_profile['dtype'] = 'uint16'  # Segment ID'leri için
        segments_dst = rasterio.open(field_segments_path, 'w', **segments_profile)

        # Colored fields için dosya (RGB)
        colored_profile = output_profile.copy()
        colored_profile.update({
            'count': 3,  # RGB
            'dtype': 'uint8'
        })
        colored_dst = rasterio.open(colored_fields_path, 'w', **colored_profile)
        
        # Processing parametreleri
        tile_size = 16384  # 16K x 16K tile'lar (güvenli boyut)
        overlap = 512      # Overlap
        
        # Progress tracking
        total_tiles = ((height + tile_size - 1) // tile_size) * ((width + tile_size - 1) // tile_size)
        print(f"📦 Toplam {total_tiles} tile işlenecek (tile size: {tile_size}x{tile_size})")
        print(f"⚡ Bu büyüklükte yaklaşık {total_tiles * 0.5:.1f} dakika sürebilir")
        
        tile_count = 0
        
        # Görselleştirme için küçük önizleme oluştur
        preview_scale = max(1, max(height, width) // 1024)  # Max 1K preview (hızlı)
        preview_height = height // preview_scale
        preview_width = width // preview_scale
        preview_result = np.zeros((preview_height, preview_width), dtype=np.float32)
        
        # Zaman takibi
        start_time = time.time()
        
        # Tile-by-tile işleme
        for y in range(0, height, tile_size):
            for x in range(0, width, tile_size):
                tile_count += 1
                
                # Her tile için progress ve ETA
                current_time = time.time()
                elapsed = current_time - start_time
                
                if tile_count <= 5 or tile_count % 5 == 0:
                    progress = (tile_count / total_tiles) * 100
                    if tile_count > 1:
                        avg_time_per_tile = elapsed / (tile_count - 1)
                        remaining_tiles = total_tiles - tile_count
                        eta_seconds = remaining_tiles * avg_time_per_tile
                        eta_minutes = eta_seconds / 60
                        print(f"🔄 Tile {tile_count}/{total_tiles} ({progress:.1f}%) - ETA: {eta_minutes:.1f} dakika")
                    else:
                        print(f"🔄 Tile {tile_count}/{total_tiles} ({progress:.1f}%)")
                
                # Tile sınırları
                y_end = min(height, y + tile_size)
                x_end = min(width, x + tile_size)
                
                try:
                    print(f"📖 Tile okuma başlıyor: window({x}, {y}, {x_end-x}, {y_end-y})")

                    # Tile'ı oku
                    window = Window(x, y, x_end - x, y_end - y)
                    tile_data = src.read(window=window)

                    print(f"📊 Okunan tile shape: {tile_data.shape}, dtype: {tile_data.dtype}")

                    # Transpose (H, W, C)
                    if tile_data.shape[0] <= 4:
                        tile_data = np.transpose(tile_data, (1, 2, 0))
                        if tile_data.shape[2] == 1:
                            tile_data = tile_data.squeeze(-1)

                    print(f"📊 İşlenecek tile shape: {tile_data.shape}")

                    # Model inference (mini-chunks)
                    print(f"🤖 Model inference başlıyor...")
                    tile_result = self._process_tile_in_chunks(tile_data)
                    print(f"✅ Model inference tamamlandı: {tile_result.shape}, min={tile_result.min():.3f}, max={tile_result.max():.3f}")

                    # Otsu thresholding
                    print(f"🎯 Otsu thresholding başlıyor...")
                    tile_binary, _ = simple_otsu_sliding_window_32x32(tile_result)
                    print(f"✅ Otsu tamamlandı: binary pixels = {np.sum(tile_binary > 0)}")

                    # Threshold map
                    print(f"🗺️ Threshold map oluşturuluyor...")
                    tile_threshold = self._create_threshold_map(tile_result, tile_binary)
                    print(f"✅ Threshold map tamamlandı")

                    # Field segmentation
                    print(f"🌾 Field segmentation başlıyor...")
                    field_segments, num_segments = self._extract_field_segments(tile_binary)
                    print(f"✅ {num_segments} tarla segmenti bulundu")

                    # Field clustering ve renklendirme
                    if num_segments > 0:
                        print(f"🎨 Field clustering başlıyor ({num_segments} segment → {self.num_colors} renk)...")
                        clustered_fields = self._cluster_fields_by_proximity(field_segments, num_segments, self.num_colors)
                        colored_fields = self._create_colored_field_map(clustered_fields, min(self.num_colors, num_segments))
                        print(f"✅ Renklendirme tamamlandı")
                    else:
                        print(f"⚠️ Tarla segmenti bulunamadı, boş çıktı oluşturuluyor")
                        clustered_fields = np.zeros_like(field_segments, dtype=np.uint16)
                        colored_fields = np.zeros((tile_result.shape[0], tile_result.shape[1], 3), dtype=np.uint8)

                    # Sonuçları dosyalara yaz
                    print(f"💾 Dosyalara yazma başlıyor...")
                    continuous_dst.write(tile_result.astype(np.float32), 1, window=window)
                    binary_dst.write((tile_binary * 255).astype(np.uint8), 1, window=window)
                    threshold_dst.write(tile_threshold.astype(np.float32), 1, window=window)
                    segments_dst.write(clustered_fields.astype(np.uint16), 1, window=window)

                    # RGB channels için colored fields yaz
                    for channel in range(3):
                        colored_dst.write(colored_fields[:, :, channel], channel + 1, window=window)

                    print(f"✅ Tile {tile_count} başarıyla işlendi ve kaydedildi")
                    
                    # Preview için downsample
                    if preview_scale > 1:
                        preview_y = y // preview_scale
                        preview_x = x // preview_scale
                        preview_y_end = min(preview_height, preview_y + tile_result.shape[0] // preview_scale)
                        preview_x_end = min(preview_width, preview_x + tile_result.shape[1] // preview_scale)
                        
                        if preview_y < preview_y_end and preview_x < preview_x_end:
                            downsampled = cv2.resize(tile_result, 
                                                   (preview_x_end - preview_x, preview_y_end - preview_y))
                            preview_result[preview_y:preview_y_end, preview_x:preview_x_end] = downsampled
                    else:
                        preview_result[y:y_end, x:x_end] = tile_result
                    
                    # GPU belleğini temizle
                    torch.cuda.empty_cache()
                    
                except Exception as e:
                    import traceback
                    print(f"⚠️ Tile {tile_count} işlenirken hata: {e}")
                    print(f"🔍 Hata detayı: {traceback.format_exc()}")
                    # Boş tile yaz
                    empty_tile = np.zeros((y_end - y, x_end - x), dtype=np.float32)
                    continuous_dst.write(empty_tile.astype(np.float32), 1, window=window)
                    binary_dst.write(empty_tile.astype(np.uint8), 1, window=window)
                    threshold_dst.write(empty_tile.astype(np.float32), 1, window=window)
                    continue
        
        # Dosyaları kapat
        continuous_dst.close()
        binary_dst.close()
        threshold_dst.close()
        segments_dst.close()
        colored_dst.close()
        
        print("✅ Streaming processing tamamlandı!")
        
        # Görselleştirme oluştur (preview üzerinden)
        print("🎨 Görselleştirme oluşturuluyor...")
        try:
            # Preview için binary mask oluştur
            preview_binary, _ = simple_otsu_sliding_window_32x32(preview_result)
            
            # RGB preview oluştur
            rgb_preview = np.stack([preview_result] * 3, axis=-1)
            rgb_preview = (rgb_preview - rgb_preview.min()) / (rgb_preview.max() - rgb_preview.min())
            rgb_preview = (rgb_preview * 255).astype(np.uint8)
            
            # Görselleştirme
            vis_preview = visualize_field_detection(rgb_preview, preview_binary)
            cv2.imwrite(visualization_path, vis_preview)
            
        except Exception as e:
            print(f"⚠️ Görselleştirme hatası: {e}")
        
        # İstatistikler hesapla (binary dosyadan örnekleme ile)
        print("📊 İstatistikler hesaplanıyor...")
        try:
            # Binary dosyadan rastgele örnekleme
            sample_stats = self._calculate_large_file_stats(binary_path, height, width, transform, crs)
        except Exception as e:
            print(f"⚠️ İstatistik hesaplama hatası: {e}")
            sample_stats = {'coverage_percentage': 0, 'field_area_hectares': 0}
        
        print(f"\n✅ Büyük dosya işlemi tamamlandı!")
        print(f"📁 Çıktı klasörü: {output_dir}/")
        print(f"   📄 {os.path.basename(continuous_path)} - Model çıktısı")
        print(f"   📄 {os.path.basename(binary_path)} - İkili sonuç")
        print(f"   📄 {os.path.basename(threshold_path)} - Eşik haritası")
        print(f"   📄 {os.path.basename(field_segments_path)} - Tarla segmentleri")
        print(f"   📄 {os.path.basename(colored_fields_path)} - Renklendirilmiş tarlalar ({self.num_colors} renk)")
        print(f"   📄 {os.path.basename(visualization_path)} - Görselleştirme (preview)")
        
        if sample_stats.get('field_area_hectares'):
            print(f"🌾 Estimated Alan: {sample_stats['field_area_hectares']:.1f} hektar")
            print(f"📊 Estimated Kapsama: %{sample_stats['coverage_percentage']:.1f}")
        
        return {
            'continuous_output': continuous_path,
            'binary_output': binary_path,
            'threshold_map': threshold_path,
            'field_segments': field_segments_path,
            'colored_fields': colored_fields_path,
            'visualization': visualization_path,
            'field_area_hectares': sample_stats.get('field_area_hectares'),
            'coverage_percentage': sample_stats.get('coverage_percentage', 0),
            'num_colors_used': self.num_colors
        }
    
    def _process_tile_in_chunks(self, tile_data):
        """Tile'ı 1024x1024 pencerelerle işle - GPU optimized batch processing"""
        tile_height, tile_width = tile_data.shape[:2]

        print(f"🔧 Tile işleniyor: {tile_height}x{tile_width}")

        # Model parametreleri - büyük dosyalar için optimize edilmiş
        window_size = 1024  # Model için 1024x1024 pencere
        step_size = 1024    # Overlap azaltıldı (daha hızlı işlem)

        # Padding ekle
        padding = window_size // 4  # Padding azaltıldı (256 px)

        print(f"📐 Window size: {window_size}, Step size: {step_size}, Padding: {padding}")

        # Tile'ı pad et
        if len(tile_data.shape) == 2:  # Grayscale
            padded_tile = np.pad(tile_data, ((padding, padding), (padding, padding)), mode='reflect')
        else:  # RGB/Multi-channel
            padded_tile = np.pad(tile_data, ((padding, padding), (padding, padding), (0, 0)), mode='reflect')

        print(f"📦 Padded tile shape: {padded_tile.shape}")

        # Batch processing için patches toplama
        patches = []
        positions = []
        patch_count = 0

        # Toplam patch sayısını hesapla
        total_patches = ((tile_height + step_size - 1) // step_size) * ((tile_width + step_size - 1) // step_size)
        print(f"🔢 Toplam {total_patches} patch işlenecek")

        # 1024x1024 pencerelerle işleme
        for y in range(0, tile_height, step_size):
            for x in range(0, tile_width, step_size):
                patch_count += 1

                # Progress her 10 patch'te bir
                if patch_count % max(1, total_patches // 10) == 0 or patch_count <= 5:
                    progress = (patch_count / total_patches) * 100
                    print(f"🔄 Patch {patch_count}/{total_patches} ({progress:.1f}%)")

                # Pencere sınırları
                py_start = y
                px_start = x
                py_end = min(y + window_size, tile_height)
                px_end = min(x + window_size, tile_width)

                # Gerçek pencere boyutu
                actual_h = py_end - py_start
                actual_w = px_end - px_start

                # Padded tile'dan pencereyi al (padding offset ile)
                pad_y_start = py_start + padding
                pad_x_start = px_start + padding
                pad_y_end = pad_y_start + actual_h
                pad_x_end = pad_x_start + actual_w

                if len(padded_tile.shape) == 2:
                    window_patch = padded_tile[pad_y_start:pad_y_end, pad_x_start:pad_x_end]
                else:
                    window_patch = padded_tile[pad_y_start:pad_y_end, pad_x_start:pad_x_end, :]

                # 1024x1024'e resize et
                if actual_h != window_size or actual_w != window_size:
                    if len(window_patch.shape) == 2:
                        window_patch = cv2.resize(window_patch, (window_size, window_size))
                    else:
                        window_patch = cv2.resize(window_patch, (window_size, window_size))

                patches.append(window_patch)
                positions.append((py_start, px_start, py_end, px_end, actual_h, actual_w))

        print(f"✅ {len(patches)} patch hazırlandı, GPU inference başlıyor...")

        # Batch inference (GPU optimized)
        predictions = self._batch_inference(patches, positions)

        print(f"🔗 Seamless blending başlıyor...")

        # Seamless blending ile predictions'ları birleştir
        result = self._seamless_blend_predictions_optimized(predictions, positions, (tile_height, tile_width))

        print(f"✅ Tile işleme tamamlandı: {result.shape}")

        return result

    def _batch_inference(self, patches, positions):
        """GPU optimized batch inference"""
        predictions = []
        batch_size = 4  # GPU memory'ye göre ayarlanabilir

        print(f"🚀 Batch inference başlıyor (batch_size={batch_size})")

        for i in range(0, len(patches), batch_size):
            batch_patches = patches[i:i+batch_size]
            batch_positions = positions[i:i+batch_size]

            batch_progress = ((i + len(batch_patches)) / len(patches)) * 100
            print(f"🔄 GPU Batch {i//batch_size + 1}/{(len(patches) + batch_size - 1)//batch_size} ({batch_progress:.1f}%)")

            try:
                # Batch tensor oluştur
                batch_tensors = []
                for patch in batch_patches:
                    patch_tensor = self._preprocess_patch(patch)
                    batch_tensors.append(patch_tensor)

                # Batch inference
                batch_predictions = []
                for j, patch_tensor in enumerate(batch_tensors):
                    try:
                        prediction = self._inference_patch(patch_tensor)

                        # Orijinal boyuta resize et
                        _, _, _, _, actual_h, actual_w = batch_positions[j]
                        if prediction.shape != (actual_h, actual_w):
                            prediction = cv2.resize(prediction, (actual_w, actual_h))

                        batch_predictions.append(prediction)

                    except Exception as e:
                        print(f"⚠️ Patch {i+j} inference hatası: {e}")
                        # Hata durumunda sıfır prediction
                        _, _, _, _, actual_h, actual_w = batch_positions[j]
                        zero_pred = np.zeros((actual_h, actual_w), dtype=np.float32)
                        batch_predictions.append(zero_pred)

                predictions.extend(batch_predictions)

                # GPU memory temizle
                torch.cuda.empty_cache()

            except Exception as e:
                print(f"❌ Batch {i//batch_size + 1} hatası: {e}")
                # Tüm batch için sıfır predictions
                for _, _, _, _, actual_h, actual_w in batch_positions:
                    zero_pred = np.zeros((actual_h, actual_w), dtype=np.float32)
                    predictions.append(zero_pred)

        print(f"✅ Batch inference tamamlandı: {len(predictions)} prediction")
        return predictions

    def _seamless_blend_predictions_optimized(self, predictions, positions, original_shape):
        """Optimized seamless blending"""
        print(f"🔗 Seamless blending: {len(predictions)} prediction → {original_shape}")

        result = np.zeros(original_shape[:2], dtype=np.float32)
        weight_map = np.zeros(original_shape[:2], dtype=np.float32)

        overlap_width = 64  # Reduced overlap for speed

        for i, (prediction, (y_start, x_start, y_end, x_end, _, _)) in enumerate(zip(predictions, positions)):
            if i % max(1, len(predictions) // 10) == 0:
                progress = (i / len(predictions)) * 100
                print(f"🔗 Blending {i}/{len(predictions)} ({progress:.1f}%)")

            pred_height, pred_width = prediction.shape

            # Simple gaussian weights (faster)
            weights = np.ones((pred_height, pred_width), dtype=np.float32)

            # Edge feathering
            fade_width = min(overlap_width, min(pred_height, pred_width) // 4)
            for edge in range(fade_width):
                weight = edge / fade_width
                weights[edge, :] *= weight  # top
                weights[-(edge+1), :] *= weight  # bottom
                weights[:, edge] *= weight  # left
                weights[:, -(edge+1)] *= weight  # right

            # Add to result
            result[y_start:y_end, x_start:x_end] += prediction * weights
            weight_map[y_start:y_end, x_start:x_end] += weights

        # Normalize
        weight_map[weight_map == 0] = 1
        result = result / weight_map

        print(f"✅ Seamless blending tamamlandı")
        return result

    def _calculate_large_file_stats(self, binary_path, height, width, transform, crs):
        """Büyük dosyalar için örnekleme ile istatistik hesapla"""
        sample_size = 1024  # 1K x 1K samples
        num_samples = 25    # 25 sample
        
        total_field_pixels = 0
        total_sample_pixels = 0
        
        with rasterio.open(binary_path) as src:
            for _ in range(num_samples):
                # Rastgele konum seç
                y = np.random.randint(0, max(1, height - sample_size))
                x = np.random.randint(0, max(1, width - sample_size))
                
                # Sample'ı oku
                window = Window(x, y, sample_size, sample_size)
                try:
                    sample_data = src.read(1, window=window)
                    field_pixels = np.sum(sample_data > 127)
                    sample_pixels = sample_data.size
                    
                    total_field_pixels += field_pixels
                    total_sample_pixels += sample_pixels
                except:
                    continue
        
        if total_sample_pixels == 0:
            return {'coverage_percentage': 0, 'field_area_hectares': 0}
        
        # Tahmin
        coverage_percentage = (total_field_pixels / total_sample_pixels) * 100
        
        if transform is not None and crs is not None:
            pixel_area_m2 = abs(transform[0] * transform[4])
            total_area_m2 = height * width * pixel_area_m2
            field_area_m2 = total_area_m2 * (coverage_percentage / 100)
            field_area_hectares = field_area_m2 / 10000
        else:
            field_area_hectares = 0
        
        return {
            'coverage_percentage': coverage_percentage,
            'field_area_hectares': field_area_hectares
        }

def main():
    """Ana fonksiyon"""
    import argparse

    parser = argparse.ArgumentParser(description='Raster Pipeline (Geotiff/ESW) - Gelişmiş Tarla Segmentasyonu')
    parser.add_argument('--input', required=True, help='Giriş raster dosyası (.tif/.tiff/.esw/.ecw)')
    parser.add_argument('--config', default='configs/cod-sam-vit-h.yaml',
                       help='Model config dosyası')
    parser.add_argument('--checkpoint', default='save/checkpoint_14.pth',
                       help='Model checkpoint dosyası')
    parser.add_argument('--num-colors', type=int, default=24,
                       help='Tarla renklendirmesi için kullanılacak renk sayısı (varsayılan: 24)')

    args = parser.parse_args()

    # Dosya kontrolü
    if not os.path.exists(args.input):
        print(f"❌ Hata: {args.input} dosyası bulunamadı!")
        return

    if not os.path.exists(args.config):
        print(f"❌ Hata: {args.config} dosyası bulunamadı!")
        return

    if not os.path.exists(args.checkpoint):
        print(f"❌ Hata: {args.checkpoint} dosyası bulunamadı!")
        return

    # Renk sayısı kontrolü
    if args.num_colors < 1 or args.num_colors > 256:
        print(f"❌ Hata: Renk sayısı 1-256 arasında olmalıdır! Verilen: {args.num_colors}")
        return

    # Processor oluştur ve çalıştır
    processor = GeotiffProcessor(args.config, args.checkpoint, args.num_colors)
    results = processor.process_geotiff(args.input)

    if results:
        print("🎉 Raster pipeline başarıyla tamamlandı!")
        print(f"🎨 {results['num_colors_used']} farklı renk kullanıldı")
        if results.get('field_area_hectares'):
            print(f"🌾 Toplam tarla alanı: {results['field_area_hectares']:.1f} hektar")
    else:
        print("❌ Pipeline işlemi başarısız!")

if __name__ == "__main__":
    main()