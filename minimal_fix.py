#!/usr/bin/env python3
"""
Minimal ve hızlı çözüm - sadece temel işlemler
"""

import rasterio
import numpy as np
import cv2

def minimal_fix():
    """Minimal çözüm"""
    
    print("⚡ Minimal çözüm başlıyor...")
    
    # Model çıktısını oku
    with rasterio.open("tiff_result/asd_center_5k_continuous.tif") as src:
        model_output = src.read(1)
        profile = src.profile
    
    print(f"📊 Model range: {model_output.min():.3f} - {model_output.max():.3f}")
    print(f"📊 Model mean: {model_output.mean():.3f}")
    
    # Sorun: Model çıktısı çok yüksek (0.8+ ortalama)
    # Çözüm: Tersini al
    inverted = 1.0 - model_output
    print(f"📊 Inverted range: {inverted.min():.3f} - {inverted.max():.3f}")
    
    # Agresif threshold - sadece en yüksek %2
    threshold = np.percentile(inverted, 98)
    print(f"📊 98th percentile threshold: {threshold:.3f}")
    
    # Binary mask
    binary = (inverted > threshold).astype(np.uint8) * 255
    ratio = np.sum(binary > 127) / binary.size
    print(f"📊 Binary ratio: {ratio:.3f}")
    
    # Basit temizlik
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
    
    # Tarla bölgeleri (sınırların tersi)
    fields = 255 - cleaned
    
    # Büyük kernel ile temizlik
    kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (20, 20))
    fields_clean = cv2.morphologyEx(fields, cv2.MORPH_CLOSE, kernel_large, iterations=2)
    
    # Connected components
    num_labels, labels = cv2.connectedComponents(fields_clean)
    print(f"📊 Toplam bileşen: {num_labels-1}")
    
    # Alan filtresi - sadece büyük alanlar
    min_area = 50000  # 50K pixels
    field_count = 0
    
    for label_id in range(1, num_labels):
        area = np.sum(labels == label_id)
        if area >= min_area:
            field_count += 1
            print(f"   Field {field_count}: {area} pixels")
    
    print(f"✅ {field_count} büyük tarla bulundu")
    
    # Sonuçları kaydet
    with rasterio.open("tiff_result/minimal_boundaries.tif", 'w', **profile) as dst:
        dst.write(cleaned, 1)
    
    with rasterio.open("tiff_result/minimal_fields.tif", 'w', **profile) as dst:
        dst.write(fields_clean, 1)
    
    print("💾 Minimal sonuçlar kaydedildi")
    
    return field_count

if __name__ == "__main__":
    count = minimal_fix()
    
    print(f"\n✅ Minimal çözüm tamamlandı! {count} tarla bulundu.")
    
    print("\n🔍 TEMEL SORUN:")
    print("Model çıktısı ters - tarla içleri yüksek, sınırlar düşük değer alıyor")
    
    print("\n💡 ÇÖZÜM:")
    print("1. Model çıktısını ters çevir: 1 - output")
    print("2. Yüksek percentile threshold (98th+)")
    print("3. Morphological operations ile temizlik")
    print("4. Büyük alan filtresi")
    
    print("\n📋 SONUÇ DOSYALARI:")
    print("- minimal_boundaries.tif (tarla sınırları)")
    print("- minimal_fields.tif (tarla bölgeleri)")
