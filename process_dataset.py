import argparse
import os
import yaml
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import glob
from torchvision import transforms
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue
import time
from tqdm import tqdm

from models import make as model_make
from models import sam  # SAM model register edilsin
import utils

def load_model(config_path, model_path):
    """Load the trained model"""
    with open(config_path, 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    
    # Create model
    model = model_make(config['model']).cuda()
    
    # Load checkpoint
    print(f"Loading model from {model_path}")
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # Check if checkpoint contains model_state_dict
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'], strict=True)
        print(f"Model successfully loaded from epoch {checkpoint.get('epoch', 'unknown')}")
    else:
        model.load_state_dict(checkpoint, strict=True)
        print("Model successfully loaded")
    
    model.eval()
    return model

def preprocess_image(image_path, size=1024):
    """Load and preprocess a single image"""
    try:
        # Load image
        image = Image.open(image_path)
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        original_size = image.size
    
        # Resize
        image = image.resize((size, size), Image.BILINEAR)
    
        # Convert to tensor and normalize
        tensor = transforms.ToTensor()(image)
        tensor = (tensor - 0.5) / 0.5  # Normalize to [-1, 1] matching config
        
        return tensor, original_size
    except Exception as e:
        print(f"Error preprocessing {image_path}: {e}")
        return None, None

def preprocess_batch(image_paths, size=1024):
    """Preprocess a batch of images"""
    batch_tensors = []
    original_sizes = []
    valid_filenames = []
    
    for image_path in image_paths:
        tensor, original_size = preprocess_image(image_path, size)
        if tensor is not None:
            batch_tensors.append(tensor)
            original_sizes.append(original_size)
            valid_filenames.append(os.path.basename(image_path).replace('.jpg', '.png'))
    
    if len(batch_tensors) == 0:
        return None, None, None
    
    batch_tensor = torch.stack(batch_tensors)
    return batch_tensor, original_sizes, valid_filenames

def get_processed_files(output_dir):
    """Get list of already processed files to enable resume functionality"""
    if not os.path.exists(output_dir):
        return set()
    
    processed_files = set()
    for f in os.listdir(output_dir):
        if f.endswith('.png'):
            # Remove .png and add .jpg to match input format
            base_name = f.replace('.png', '.jpg')
            processed_files.add(base_name)
    
    return processed_files

def filter_remaining_files(image_files, processed_files):
    """Filter out already processed files"""
    remaining = []
    for img_file in image_files:
        base_name = os.path.basename(img_file)
        if base_name not in processed_files:
            remaining.append(img_file)
    return remaining

def apply_threshold(mask, threshold=0.5):
    """Apply binary threshold to mask"""
    return (mask > threshold).astype(np.uint8) * 255

def process_dataset_split(model, input_dir, output_dir, split_name, batch_size=4, threshold=0.5, save_binary=True):
    """Process a single dataset split with resume capability and optimizations"""
    print(f"\n=== Processing {split_name} split ===")
    
    # Create output directory for this split
    split_output_dir = os.path.join(output_dir, f"{split_name}_model_output")
    os.makedirs(split_output_dir, exist_ok=True)
    
    # Binary output directory if requested
    if save_binary:
        binary_output_dir = os.path.join(output_dir, f"{split_name}_binary_output")
        os.makedirs(binary_output_dir, exist_ok=True)
    
    # Get all images from the split directory
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.JPG', '*.JPEG', '*.PNG']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_dir, ext)))
    
    # Filter out annotation files
    image_files = [f for f in image_files if not f.endswith('_annotations.coco.json')]
    
    if len(image_files) == 0:
        print(f"No images found in {input_dir}")
        return
    
    print(f"Found {len(image_files)} images in {split_name} split")
    
    # Zaten işlenmiş dosyaları kontrol et
    processed_files = get_processed_files(split_output_dir)
    print(f"Already processed: {len(processed_files)} images")
    
    # İşlenmemiş dosyaları filtrele
    remaining_files = filter_remaining_files(image_files, processed_files)
    print(f"Remaining to process: {len(remaining_files)} images")
    
    if len(remaining_files) == 0:
        print(f"All images in {split_name} split have already been processed!")
        return
    
    # Batch processing için dosyaları grupla
    total_processed = len(processed_files)
    processed_count = 0
    
    # Progress bar
    pbar = tqdm(total=len(remaining_files), desc=f"Processing {split_name}")
    
    with torch.no_grad():
        # Process in batches
        for i in range(0, len(remaining_files), batch_size):
            batch_files = remaining_files[i:i+batch_size]
            
            try:
                # Preprocess batch
                batch_tensor, original_sizes, filenames = preprocess_batch(batch_files)
                
                if batch_tensor is None:
                    pbar.update(len(batch_files))
                    continue
                
                # Move to GPU
                batch_tensor = batch_tensor.cuda()
                
                # Run batch inference
                batch_predictions = []
                for j in range(batch_tensor.size(0)):
                    pred = torch.sigmoid(model.infer(batch_tensor[j:j+1]))
                    batch_predictions.append(pred[0, 0].cpu().numpy())
                
                # Process and save results
                for j, (mask, original_size, filename) in enumerate(zip(batch_predictions, original_sizes, filenames)):
                    try:
                        # Continuous (grayscale) output
                        mask_continuous = (mask * 255).astype(np.uint8)
                        mask_resized = Image.fromarray(mask_continuous, mode='L')
                        mask_resized = mask_resized.resize(original_size, Image.BILINEAR)
                        
                        # Save continuous output
                        output_path = os.path.join(split_output_dir, filename)
                        mask_resized.save(output_path)
                        
                        # Binary output if requested
                        if save_binary:
                            mask_binary = apply_threshold(mask, threshold)
                            mask_binary_resized = Image.fromarray(mask_binary, mode='L')
                            mask_binary_resized = mask_binary_resized.resize(original_size, Image.NEAREST)
                            
                            # Save binary output
                            binary_output_path = os.path.join(binary_output_dir, filename)
                            mask_binary_resized.save(binary_output_path)
                        
                        processed_count += 1
                        
                    except Exception as e:
                        print(f"Error saving {filename}: {e}")
                        continue
                
                # Clear GPU memory
                del batch_tensor, batch_predictions
                torch.cuda.empty_cache()
                
                # Update progress
                pbar.update(len(batch_files))
                
                # Her 50 dosyada bir progress bilgisi
                if processed_count % 50 == 0:
                    progress_percent = ((total_processed + processed_count) / len(image_files)) * 100
                    pbar.set_postfix({
                        'completed': f"{total_processed + processed_count}/{len(image_files)}",
                        'progress': f"{progress_percent:.1f}%"
                    })
                
            except Exception as e:
                print(f"Error processing batch starting at index {i}: {e}")
                pbar.update(len(batch_files))
                continue
    
    pbar.close()
    
    total_final = total_processed + processed_count
    print(f"Successfully processed {processed_count} new images from {split_name} split")
    print(f"Total completed: {total_final}/{len(image_files)} images")
    print(f"Continuous results saved to {split_output_dir}")
    if save_binary:
        print(f"Binary results (threshold={threshold}) saved to {binary_output_dir}")

def process_dataset_split_threaded(model, input_dir, output_dir, split_name, batch_size=4, num_threads=2, threshold=0.5, save_binary=True):
    """Ultra-fast processing with threading and batching"""
    print(f"\n=== Processing {split_name} split (Multi-threaded) ===")
    
    # Create output directory for this split
    split_output_dir = os.path.join(output_dir, f"{split_name}_model_output")
    os.makedirs(split_output_dir, exist_ok=True)
    
    # Binary output directory if requested
    if save_binary:
        binary_output_dir = os.path.join(output_dir, f"{split_name}_binary_output")
        os.makedirs(binary_output_dir, exist_ok=True)
    
    # Get all images from the split directory
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.JPG', '*.JPEG', '*.PNG']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_dir, ext)))
    
    image_files = [f for f in image_files if not f.endswith('_annotations.coco.json')]
    
    if len(image_files) == 0:
        print(f"No images found in {input_dir}")
        return
    
    # Check already processed files
    processed_files = get_processed_files(split_output_dir)
    remaining_files = filter_remaining_files(image_files, processed_files)
    
    print(f"Found {len(image_files)} total images")
    print(f"Already processed: {len(processed_files)} images")
    print(f"Remaining to process: {len(remaining_files)} images")
    
    if len(remaining_files) == 0:
        print(f"All images in {split_name} split have already been processed!")
        return
    
    # Thread-safe counters
    processed_count = threading.Value('i', 0)
    error_count = threading.Value('i', 0)
    
    # Progress bar
    pbar = tqdm(total=len(remaining_files), desc=f"Processing {split_name} (threaded)")
    
    def process_batch_thread(batch_files, thread_id):
        """Thread function to process a batch of files"""
        with torch.no_grad():
            try:
                # Preprocess batch
                batch_tensor, original_sizes, filenames = preprocess_batch(batch_files)
                
                if batch_tensor is None:
                    return
                
                # Move to GPU (each thread should use same GPU but different stream)
                batch_tensor = batch_tensor.cuda()
                
                # Process each image in batch
                for j in range(batch_tensor.size(0)):
                    try:
                        # Run inference
                        pred = torch.sigmoid(model.infer(batch_tensor[j:j+1]))
                        mask = pred[0, 0].cpu().numpy()
                        
                        # Continuous output
                        mask_continuous = (mask * 255).astype(np.uint8)
                        mask_resized = Image.fromarray(mask_continuous, mode='L')
                        mask_resized = mask_resized.resize(original_sizes[j], Image.BILINEAR)
                        
                        output_path = os.path.join(split_output_dir, filenames[j])
                        mask_resized.save(output_path)
                        
                        # Binary output if requested
                        if save_binary:
                            mask_binary = apply_threshold(mask, threshold)
                            mask_binary_resized = Image.fromarray(mask_binary, mode='L')
                            mask_binary_resized = mask_binary_resized.resize(original_sizes[j], Image.NEAREST)
                            
                            binary_output_path = os.path.join(binary_output_dir, filenames[j])
                            mask_binary_resized.save(binary_output_path)
                        
                        with processed_count.get_lock():
                            processed_count.value += 1
                        
                        pbar.update(1)
                        
                    except Exception as e:
                        with error_count.get_lock():
                            error_count.value += 1
                        print(f"Thread {thread_id} error processing {filenames[j] if j < len(filenames) else 'unknown'}: {e}")
                        pbar.update(1)
                
                # Clear memory
                del batch_tensor
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"Thread {thread_id} batch error: {e}")
                pbar.update(len(batch_files))
    
    # Create batches for threading
    batches = [remaining_files[i:i+batch_size] for i in range(0, len(remaining_files), batch_size)]
    
    # Process with ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        
        for i, batch in enumerate(batches):
            future = executor.submit(process_batch_thread, batch, i)
            futures.append(future)
        
        # Wait for all threads to complete
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"Thread execution error: {e}")
    
    pbar.close()
    
    print(f"Processing completed!")
    print(f"Successfully processed: {processed_count.value} images")
    print(f"Errors: {error_count.value} images")
    print(f"Total: {len(processed_files) + processed_count.value}/{len(image_files)} images")
    print(f"Continuous results saved to {split_output_dir}")
    if save_binary:
        print(f"Binary results (threshold={threshold}) saved to {binary_output_dir}")

def main(args):
    # Load model
    print(f"Loading model from {args.model}")
    start_time = time.time()
    model = load_model(args.config, args.model)
    print(f"Model loaded in {time.time() - start_time:.2f} seconds")
    
    # Create main output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("=== SAM-Adapter Binary Output Processing ===")
    print("Features:")
    print("- Batch processing for faster inference")
    print("- Progress bars with detailed information")
    print("- Automatic skip of already processed images")
    print("- GPU memory optimization")
    print("- Multi-threading support (use --threaded flag)")
    print(f"- Batch size: {args.batch_size}")
    print(f"- Binary threshold: {args.threshold}")
    print(f"- Save binary outputs: {args.save_binary}")
    if args.threaded:
        print(f"- Threading enabled with {args.num_threads} threads")
    print()
    
    total_start_time = time.time()
    
    # Check if dataset_dir contains splits or is a single directory
    if os.path.exists(os.path.join(args.dataset_dir, 'train')):
        # Multiple splits scenario
        splits = ['train', 'valid', 'test']
    for split in splits:
        split_dir = os.path.join(args.dataset_dir, split)
        
        if os.path.exists(split_dir):
            split_start_time = time.time()
            
            if args.threaded:
                process_dataset_split_threaded(
                    model, split_dir, args.output_dir, split,
                        batch_size=args.batch_size, num_threads=args.num_threads,
                        threshold=args.threshold, save_binary=args.save_binary
                )
            else:
                process_dataset_split(
                    model, split_dir, args.output_dir, split,
                        batch_size=args.batch_size, threshold=args.threshold,
                        save_binary=args.save_binary
                )
            
            split_time = time.time() - split_start_time
            print(f"{split} split completed in {split_time:.2f} seconds")
        else:
            print(f"Warning: {split} directory not found at {split_dir}")
    else:
        # Single directory scenario
        split_name = os.path.basename(args.dataset_dir.rstrip('/\\'))
        if not split_name:
            split_name = 'dataset'
            
        if args.threaded:
            process_dataset_split_threaded(
                model, args.dataset_dir, args.output_dir, split_name,
                batch_size=args.batch_size, num_threads=args.num_threads,
                threshold=args.threshold, save_binary=args.save_binary
            )
        else:
            process_dataset_split(
                model, args.dataset_dir, args.output_dir, split_name,
                batch_size=args.batch_size, threshold=args.threshold,
                save_binary=args.save_binary
            )
    
    total_time = time.time() - total_start_time
    
    print(f"\n=== Processing Complete ===")
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"All results saved to {args.output_dir}")
    print("Created directories:")
    
    # Check what was actually created
    for item in os.listdir(args.output_dir):
        item_path = os.path.join(args.output_dir, item)
        if os.path.isdir(item_path):
            if 'binary' in item:
                print(f"  - {item_path} (binary, threshold={args.threshold})")
            else:
                print(f"  - {item_path} (continuous/grayscale)")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='SAM-Adapter dataset processing with binary output option')
    parser.add_argument('--config', default="configs/cod-sam-vit-h.yaml", help='Path to config file')
    parser.add_argument('--model', default="save/checkpoint_14.pth", help='Path to model checkpoint')
    parser.add_argument('--dataset_dir', default="dataset", help='Path to dataset directory containing train/valid/test folders')
    parser.add_argument('--output_dir', default="dataset_model_outputs", help='Directory to save all results')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size for processing (default: 4)')
    parser.add_argument('--threshold', type=float, default=0.5, help='Binary threshold for creating binary outputs (default: 0.5)')
    parser.add_argument('--save_binary', action='store_true', default=True, help='Save binary outputs in addition to continuous')
    parser.add_argument('--no_binary', action='store_true', help='Disable binary output saving')
    parser.add_argument('--threaded', action='store_true', help='Enable multi-threading for ultra-fast processing')
    parser.add_argument('--num_threads', type=int, default=2, help='Number of threads for processing (default: 2)')
    args = parser.parse_args()
    
    # Handle binary output flag
    if args.no_binary:
        args.save_binary = False
    
    # Validate arguments
    if args.batch_size < 1:
        print("Error: batch_size must be at least 1")
        exit(1)
    
    if args.num_threads < 1:
        print("Error: num_threads must be at least 1")
        exit(1)
        
    if not (0.0 <= args.threshold <= 1.0):
        print("Error: threshold must be between 0.0 and 1.0")
        exit(1)
    
    # GPU memory check
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        recommended_batch_size = max(1, min(8, int(gpu_memory / 2)))
        
        if args.batch_size > recommended_batch_size:
            print(f"Warning: Batch size {args.batch_size} might be too large for GPU memory ({gpu_memory:.1f}GB)")
            print(f"Recommended batch size: {recommended_batch_size}")
            
        print(f"GPU: {torch.cuda.get_device_name(0)} ({gpu_memory:.1f}GB)")
    else:
        print("Warning: CUDA not available, using CPU (this will be very slow)")
    
    main(args)
