import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import argparse
from tqdm import tqdm
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from model import ResNetUNet, SimpleBinaryRefinement, BinaryRefinementUNet, CombinedLoss
from dataset import get_dataloaders

class AdvancedBinaryRefinementTrainer:
    def __init__(self, model, train_loader, val_loader, device, args):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        self.args = args
        
        # Gelişmiş loss function
        if args.loss_type == 'combined':
            self.criterion = CombinedLoss(
                alpha=0.25, gamma=2.0,
                dice_weight=0.5, bce_weight=0.3, focal_weight=0.2
            )
        elif args.loss_type == 'dice_bce':
            self.criterion = self.dice_bce_loss
        else:
            self.criterion = nn.<PERSON><PERSON>oss()
        
        # Optimizer with weight decay for better generalization
        if args.optimizer == 'adamw':
            self.optimizer = optim.AdamW(
                model.parameters(), 
                lr=args.lr, 
                weight_decay=1e-4,
                betas=(0.9, 0.999)
            )
        else:
            self.optimizer = optim.Adam(
                model.parameters(), 
                lr=args.lr, 
                weight_decay=1e-5
            )
        
        # Advanced learning rate scheduler
        if args.scheduler == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer, T_0=10, T_mult=2, eta_min=1e-6
            )
        elif args.scheduler == 'plateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='max', factor=0.5, patience=5, 
                min_lr=1e-6, verbose=True
            )
        else:
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=20, gamma=0.5
            )
        
        # Metrics tracking
        self.best_iou = 0.0
        self.train_losses = []
        self.val_losses = []
        self.train_ious = []
        self.val_ious = []
        
        # Tensorboard
        self.writer = SummaryWriter(f'runs/{args.model_type}_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        
        # Checkpoint directory
        os.makedirs('checkpoints', exist_ok=True)
    
    def dice_bce_loss(self, pred, target, smooth=1e-7):
        """Combined Dice + BCE loss"""
        # BCE loss
        bce = nn.BCELoss()(pred, target)
        
        # Dice loss
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)
        intersection = (pred_flat * target_flat).sum()
        dice_score = (2. * intersection + smooth) / (pred_flat.sum() + target_flat.sum() + smooth)
        dice_loss = 1 - dice_score
        
        return 0.5 * bce + 0.5 * dice_loss
    
    def calculate_iou(self, pred, target, threshold=0.5):
        """IoU calculation"""
        pred_binary = (pred > threshold).float()
        target_binary = target.float()
        
        intersection = (pred_binary * target_binary).sum(dim=(2, 3))
        union = pred_binary.sum(dim=(2, 3)) + target_binary.sum(dim=(2, 3)) - intersection
        
        iou = (intersection + 1e-7) / (union + 1e-7)
        return iou.mean().item()
    
    def train_epoch(self, epoch):
        self.model.train()
        running_loss = 0.0
        running_iou = 0.0
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.args.epochs}')
        
        for batch_idx, (sam_output, ground_truth) in enumerate(pbar):
            sam_output = sam_output.to(self.device, non_blocking=True)
            ground_truth = ground_truth.to(self.device, non_blocking=True)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(sam_output)
            loss = self.criterion(outputs, ground_truth)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Metrics
            iou = self.calculate_iou(outputs, ground_truth)
            running_loss += loss.item()
            running_iou += iou
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'IoU': f'{iou:.4f}'
            })
            
            # Log to tensorboard
            if batch_idx % 100 == 0:
                step = epoch * len(self.train_loader) + batch_idx
                self.writer.add_scalar('Train/BatchLoss', loss.item(), step)
                self.writer.add_scalar('Train/BatchIoU', iou, step)
        
        avg_loss = running_loss / len(self.train_loader)
        avg_iou = running_iou / len(self.train_loader)
        
        return avg_loss, avg_iou
    
    def validate(self, epoch):
        self.model.eval()
        running_loss = 0.0
        running_iou = 0.0
        
        with torch.no_grad():
            pbar = tqdm(self.val_loader, desc='Validation')
            
            for sam_output, ground_truth in pbar:
                sam_output = sam_output.to(self.device, non_blocking=True)
                ground_truth = ground_truth.to(self.device, non_blocking=True)
                
                outputs = self.model(sam_output)
                loss = self.criterion(outputs, ground_truth)
                iou = self.calculate_iou(outputs, ground_truth)
                
                running_loss += loss.item()
                running_iou += iou
        
        avg_loss = running_loss / len(self.val_loader)
        avg_iou = running_iou / len(self.val_loader)
        
        return avg_loss, avg_iou
    
    def save_checkpoint(self, epoch, is_best=False):
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_iou': self.best_iou,
            'args': self.args
        }
        
        # Latest checkpoint
        torch.save(checkpoint, 'checkpoints/latest_checkpoint.pth')
        
        if is_best:
            torch.save(checkpoint, 'checkpoints/best_model.pth')
            torch.save(self.model.state_dict(), 'checkpoints/best_model_weights.pth')
            print(f"🎉 Yeni en iyi model! IoU: {self.best_iou:.4f}")
    
    def train(self):
        print("Training başlatıldı...")
        print(f"Device: {self.device}")
        print(f"Model: {self.args.model_type}")
        print(f"Train samples: {len(self.train_loader.dataset)}")
        print(f"Val samples: {len(self.val_loader.dataset)}")
        print(f"Target IoU: 95%+")
        
        for epoch in range(self.args.epochs):
            # Training
            train_loss, train_iou = self.train_epoch(epoch)
            
            # Validation
            val_loss, val_iou = self.validate(epoch)
            
            # Learning rate scheduling
            if self.args.scheduler == 'plateau':
                self.scheduler.step(val_iou)
            elif self.args.scheduler == 'cosine':
                self.scheduler.step()
            else:
                self.scheduler.step()
            
            # Save metrics
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_ious.append(train_iou)
            self.val_ious.append(val_iou)
            
            # Tensorboard logging
            self.writer.add_scalar('Epoch/TrainLoss', train_loss, epoch)
            self.writer.add_scalar('Epoch/ValLoss', val_loss, epoch)
            self.writer.add_scalar('Epoch/TrainIoU', train_iou, epoch)
            self.writer.add_scalar('Epoch/ValIoU', val_iou, epoch)
            self.writer.add_scalar('Epoch/LR', self.optimizer.param_groups[0]['lr'], epoch)
            
            # Print epoch results
            print(f"\nEpoch {epoch+1}/{self.args.epochs}:")
            print(f"Train Loss: {train_loss:.4f}, Train IoU: {train_iou:.4f}")
            print(f"Val Loss: {val_loss:.4f}, Val IoU: {val_iou:.4f}")
            print(f"LR: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # Save best model
            if val_iou > self.best_iou:
                self.best_iou = val_iou
                self.save_checkpoint(epoch, is_best=True)
            
            # Regular checkpoint
            if (epoch + 1) % 5 == 0:
                self.save_checkpoint(epoch)
            
            # Early achievement check
            if val_iou > 0.95:
                print(f"🎯 %95+ IoU hedefine ulaşıldı! IoU: {val_iou:.4f}")
                break
        
        print(f"\n✅ Training tamamlandı!")
        print(f"En iyi IoU: {self.best_iou:.4f} ({self.best_iou*100:.2f}%)")
        
        # Close tensorboard
        self.writer.close()
        
        return self.best_iou

def main():
    parser = argparse.ArgumentParser(description='Advanced Binary Refinement Training')
    parser.add_argument('--model_type', type=str, default='resnet_unet', 
                       choices=['simple', 'unet', 'resnet_unet'],
                       help='Model architecture')
    parser.add_argument('--backbone', type=str, default='resnet34', 
                       choices=['resnet34', 'resnet50'],
                       help='ResNet backbone for ResNet-UNet')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--image_size', type=int, default=512, help='Input image size')
    parser.add_argument('--optimizer', type=str, default='adamw', 
                       choices=['adam', 'adamw'], help='Optimizer')
    parser.add_argument('--scheduler', type=str, default='cosine', 
                       choices=['step', 'plateau', 'cosine'], help='LR scheduler')
    parser.add_argument('--loss_type', type=str, default='combined', 
                       choices=['bce', 'dice_bce', 'combined'], help='Loss function')
    parser.add_argument('--sam_output_dir', type=str, 
                       default='../dataset_model_outputs/train_model_output',
                       help='SAM output directory')
    parser.add_argument('--ground_truth_dir', type=str, 
                       default='../dataset/train_mask',
                       help='Ground truth directory')
    
    args = parser.parse_args()
    
    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Model selection
    if args.model_type == 'resnet_unet':
        print(f"🚀 ResNet-UNet modeli seçildi (backbone: {args.backbone})")
        model = ResNetUNet(n_channels=3, n_classes=1, backbone=args.backbone)
    elif args.model_type == 'simple':
        print("⚡ SimpleBinaryRefinement modeli seçildi")
        model = SimpleBinaryRefinement(n_channels=3)
    else:
        print("🔄 BinaryRefinementUNet modeli seçildi")
        model = BinaryRefinementUNet(n_channels=3, n_classes=1, bilinear=True)
    
    # Data loaders
    train_loader, val_loader = get_dataloaders(
        sam_output_dir=args.sam_output_dir,
        ground_truth_dir=args.ground_truth_dir,
        batch_size=args.batch_size,
        image_size=args.image_size,
        num_workers=2  # Windows için düşük tutuyoruz
    )
    
    # Trainer
    trainer = AdvancedBinaryRefinementTrainer(model, train_loader, val_loader, device, args)
    
    # Train
    best_iou = trainer.train()
    
    return best_iou

if __name__ == '__main__':
    best_iou = main() 