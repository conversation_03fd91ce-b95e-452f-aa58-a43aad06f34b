# K-means Enhanced Fast Batch Processor

Bu do<PERSON>üman<PERSON>yon, `fast_batch_processor.py` dosyasına eklenen yeni K-means clustering özelliklerini açıklar.

## 🆕 Yeni Özellikler

### 1. K-means Clustering ile Renk Gruplandırması
- <PERSON><PERSON><PERSON> al<PERSON> artık **K-means clustering** algoritması ile gruplandırılabilir
- Kullanıcı istediği **renk grubu sayısını** belirleyebilir
- Daha homojen ve anlamlı renk grupları oluşturur

### 2. Kenar Alanları Filtreleme
- **Resin kenarlarındaki boş alanları** otomatik olarak filtreler
- Sadece gerçek tarla alanlarını renklendirir
- Kenar filtreleme açılıp kapatılabilir

### 3. Gelişmiş Özellik Çıkarımı
- Alan, çevre, kompaktlık, aspect ratio
- Ortalama renk (RGB) ve renk varyansı
- Normalize edilmiş merkez koordinatları
- Logaritmik ölçeklendirme

## 🚀 Kullanım Örnekleri

### Temel Kullanım

```bash
# 5 renk grubu ile K-means clustering
python fast_batch_processor.py --num_groups 5

# 8 renk grubu + kenar filtreleme kapalı
python fast_batch_processor.py --num_groups 8 --no_filter_edges

# Tek dosya işleme, 10 renk grubu
python fast_batch_processor.py geotiff_input/asd_center_5k.tif --num_groups 10
```

### Gelişmiş Kullanım

```bash
# Klasör işleme, 6 renk grubu, kenar filtreleme aktif
python fast_batch_processor.py --mode batch --input_dir test_images --num_groups 6 --filter_edges

# Tek dosya, otomatik grup sayısı, kenar filtreleme kapalı
python fast_batch_processor.py --mode single --single_file my_image.tif --no_filter_edges

# Özel çıktı klasörü ile
python fast_batch_processor.py --num_groups 12 --output_dir my_results --filter_edges
```

## 📊 Yeni Parametreler

| Parametre | Tip | Varsayılan | Açıklama |
|-----------|-----|------------|----------|
| `--num_groups` | int | None | Hedef renk grubu sayısı (K-means için) |
| `--filter_edges` | flag | True | Kenar alanlarını filtrele |
| `--no_filter_edges` | flag | False | Kenar filtrelemeyi devre dışı bırak |

## 🎯 Algoritma Karşılaştırması

### Geleneksel Algoritma (Eski)
1. Spatial clustering (mesafe tabanlı)
2. Renk benzerliği analizi
3. Manuel threshold'lar

### K-means Enhanced Algoritma (Yeni)
1. **Kenar alanları filtreleme**
2. **Çok boyutlu özellik çıkarımı**
3. **K-means clustering**
4. **Kullanıcı kontrollü grup sayısı**

## 🔍 Özellik Vektörü

Her tarla alanı için çıkarılan özellikler:

```python
feature_vector = [
    log(area + 1),           # Logaritmik alan
    log(perimeter + 1),      # Logaritmik çevre
    compactness,             # Şekil düzenliliği
    1.0 / aspect_ratio,      # Ters aspect ratio
    r / 255.0,               # Normalize RGB
    g / 255.0,
    b / 255.0,
    color_variance / 255.0,  # Renk varyansı
    cx,                      # Normalize merkez X
    cy                       # Normalize merkez Y
]
```

## 🧪 Test Etme

Test script'ini kullanarak yeni özellikleri test edebilirsiniz:

```bash
python test_kmeans_features.py
```

Bu script 5 farklı test senaryosu sunar:
1. 5 Grup K-means + Kenar Filtreleme
2. 8 Grup K-means + Kenar Filtreleme Kapalı
3. 10 Grup K-means + Kenar Filtreleme
4. Otomatik Gruplandırma + Kenar Filtreleme
5. Geleneksel Mod (Kenar Filtreleme Kapalı)

## 📈 Performans İyileştirmeleri

### K-means Clustering Avantajları
- **Daha homojen gruplar**: Benzer özellikteki tarlalar aynı renkte
- **Kullanıcı kontrolü**: İstenen grup sayısını belirleyebilme
- **Çok boyutlu analiz**: Sadece renk değil, şekil ve konum da dikkate alınır

### Kenar Filtreleme Avantajları
- **Temiz sonuçlar**: Resin kenarlarındaki gürültü elimine edilir
- **Gerçek tarla odaklı**: Sadece anlamlı tarla alanları renklendirilir
- **Esnek kontrol**: İsteğe bağlı açılıp kapatılabilir

## 🎨 Renk Gruplandırması Stratejileri

### Küçük Grup Sayıları (3-5)
- Ana tarla tiplerini ayırt etmek için
- Genel kategorilendirme
- Hızlı görsel analiz

### Orta Grup Sayıları (6-10)
- Detaylı tarla analizi
- Alt kategoriler
- Dengeli görselleştirme

### Büyük Grup Sayıları (10+)
- Çok detaylı segmentasyon
- Araştırma amaçlı
- Hassas analiz

## 🔧 Teknik Detaylar

### StandardScaler Normalizasyonu
- Tüm özellikler 0-1 aralığına normalize edilir
- K-means için optimal performans
- Farklı ölçeklerdeki özelliklerin eşit ağırlığı

### Hata Yönetimi
- K-means başarısız olursa otomatik olarak geleneksel algoritma devreye girer
- Graceful degradation
- Robust error handling

### Memory Optimization
- Büyük dosyalar için tile-based processing
- GPU memory management
- Efficient contour processing

## 📝 Notlar

- K-means clustering scikit-learn kütüphanesini gerektirir
- Kenar filtreleme threshold'u görüntü boyutuna göre otomatik ayarlanır
- Otomatik grup sayısı contour sayısının kareköküne göre belirlenir
