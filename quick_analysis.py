#!/usr/bin/env python3
"""
Hızlı analiz ve sorun tespiti
"""

import rasterio
import numpy as np
import matplotlib.pyplot as plt

def quick_analysis():
    """Hızlı analiz"""
    
    print("⚡ Hızlı analiz başlıyor...")
    
    # Model çıktısını oku
    with rasterio.open("tiff_result/asd_center_5k_continuous.tif") as src:
        model_output = src.read(1)
    
    print(f"📊 Model output shape: {model_output.shape}")
    print(f"📊 Model output range: {model_output.min():.3f} - {model_output.max():.3f}")
    print(f"📊 Model output mean: {model_output.mean():.3f}")
    print(f"📊 Model output std: {model_output.std():.3f}")
    
    # Histogram analizi
    hist, bins = np.histogram(model_output.flatten(), bins=100)
    
    print(f"📊 En düşük %1: {np.percentile(model_output, 1):.3f}")
    print(f"📊 En düşük %5: {np.percentile(model_output, 5):.3f}")
    print(f"📊 En düşük %10: {np.percentile(model_output, 10):.3f}")
    print(f"📊 Median: {np.percentile(model_output, 50):.3f}")
    print(f"📊 En yüksek %10: {np.percentile(model_output, 90):.3f}")
    print(f"📊 En yüksek %5: {np.percentile(model_output, 95):.3f}")
    print(f"📊 En yüksek %1: {np.percentile(model_output, 99):.3f}")
    
    # Basit threshold testleri
    thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    
    print("\n📊 Threshold analizi:")
    for thresh in thresholds:
        binary = (model_output > thresh).astype(np.uint8)
        ratio = np.sum(binary) / binary.size
        print(f"   Threshold {thresh}: {ratio:.3f} ratio")
    
    # Ters threshold testleri
    inverted = 1.0 - model_output
    print(f"\n📊 Inverted range: {inverted.min():.3f} - {inverted.max():.3f}")
    
    print("\n📊 Inverted threshold analizi:")
    for thresh in thresholds:
        binary = (inverted > thresh).astype(np.uint8)
        ratio = np.sum(binary) / binary.size
        print(f"   Inverted threshold {thresh}: {ratio:.3f} ratio")
    
    # Percentile threshold testleri
    print("\n📊 Percentile threshold analizi:")
    percentiles = [90, 95, 97, 99, 99.5, 99.9]
    for p in percentiles:
        thresh = np.percentile(inverted, p)
        binary = (inverted > thresh).astype(np.uint8)
        ratio = np.sum(binary) / binary.size
        print(f"   {p}th percentile ({thresh:.3f}): {ratio:.3f} ratio")
    
    # Görselleştirme
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # Model output
    axes[0, 0].imshow(model_output, cmap='viridis')
    axes[0, 0].set_title('Model Output')
    axes[0, 0].axis('off')
    
    # Inverted
    axes[0, 1].imshow(inverted, cmap='viridis')
    axes[0, 1].set_title('Inverted (1 - model)')
    axes[0, 1].axis('off')
    
    # Histogram
    axes[0, 2].hist(model_output.flatten(), bins=100, alpha=0.7, label='Original')
    axes[0, 2].hist(inverted.flatten(), bins=100, alpha=0.7, label='Inverted')
    axes[0, 2].set_title('Histograms')
    axes[0, 2].legend()
    
    # Test thresholds
    test_thresh = 0.3
    binary_orig = (model_output > test_thresh).astype(np.uint8) * 255
    axes[1, 0].imshow(binary_orig, cmap='gray')
    axes[1, 0].set_title(f'Original > {test_thresh}')
    axes[1, 0].axis('off')
    
    binary_inv = (inverted > test_thresh).astype(np.uint8) * 255
    axes[1, 1].imshow(binary_inv, cmap='gray')
    axes[1, 1].set_title(f'Inverted > {test_thresh}')
    axes[1, 1].axis('off')
    
    # Percentile threshold
    perc_thresh = np.percentile(inverted, 95)
    binary_perc = (inverted > perc_thresh).astype(np.uint8) * 255
    axes[1, 2].imshow(binary_perc, cmap='gray')
    axes[1, 2].set_title(f'95th percentile ({perc_thresh:.3f})')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/quick_analysis.png', dpi=150, bbox_inches='tight')
    print("💾 Hızlı analiz kaydedildi: tiff_result/quick_analysis.png")
    plt.show()
    
    print("\n🔍 SORUN TESPİTİ:")
    print("1. Model çıktısı çok yüksek değerler (ortalama ~0.8)")
    print("2. Bu, modelin tarla içlerini 'pozitif' olarak öğrendiği anlamına geliyor")
    print("3. Tarla sınırları düşük değerler alıyor")
    print("4. Threshold stratejisini tersine çevirmek gerekiyor")
    
    print("\n💡 ÇÖZÜM ÖNERİLERİ:")
    print("1. Model çıktısını ters çevir: 1 - output")
    print("2. Yüksek percentile threshold kullan (95th+)")
    print("3. Agresif morphological operations")
    print("4. Connected components ile alan filtresi")

if __name__ == "__main__":
    quick_analysis()
