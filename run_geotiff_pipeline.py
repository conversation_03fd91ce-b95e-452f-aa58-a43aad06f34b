#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Raster Pipeline Test Script
Bu script, geotiff_pipeline.py dosyasını Geotiff (.tif) ve ESW (.esw) dosyalarını test etmek için k<PERSON>.
"""

import os
import sys
from geotiff_pipeline import GeotiffProcessor

def test_pipeline():
    """Pipeline'ı test et"""
    
    print("🧪 Geotiff Pipeline Test Başlatılıyor...")
    
    # Gerekli dosyaları kontrol et
    config_path = 'configs/cod-sam-vit-h.yaml'
    checkpoint_path = 'save/checkpoint_14.pth'
    
    if not os.path.exists(config_path):
        print(f"❌ Hata: {config_path} dosyası bulunamadı!")
        print("Lütfen model config dosyasının doğru yolda olduğundan emin olun.")
        return False
        
    if not os.path.exists(checkpoint_path):
        print(f"❌ Hata: {checkpoint_path} dosyası bulunamadı!")
        print("Lütfen model checkpoint dosyasının doğru yolda olduğundan emin olun.")
        return False
    
    # Test geotiff dosyası yolu
    test_geotiff = "test_images/your_geotiff_file.tif"  # Buraya geotiff dosyanızın yolunu yazın
    
    if not os.path.exists(test_geotiff):
        print(f"❌ Test geotiff dosyası bulunamadı: {test_geotiff}")
        print("Lütfen test etmek istediğiniz geotiff dosyasının yolunu güncelleyin.")
        
        # Alternatif dosya önerileri
        print("\n📂 Mevcut test_images klasörü içeriği:")
        if os.path.exists("test_images"):
            for file in os.listdir("test_images"):
                if file.lower().endswith(('.tif', '.tiff', '.geotiff')):
                    print(f"   - {file}")
        else:
            print("   test_images klasörü bulunamadı.")
        
        return False
    
    try:
        # Processor oluştur
        print("🚀 GeotiffProcessor başlatılıyor...")
        processor = GeotiffProcessor(config_path, checkpoint_path)
        
        # Pipeline'ı çalıştır
        print(f"🌍 Geotiff işleniyor: {test_geotiff}")
        results = processor.process_geotiff(test_geotiff)
        
        print("\n✅ Test başarıyla tamamlandı!")
        print("📁 Oluşturulan dosyalar:")
        for key, path in results.items():
            print(f"   - {key}: {path}")
            
        return True
        
    except Exception as e:
        print(f"❌ Test sırasında hata oluştu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def list_available_geotiffs():
    """Mevcut raster dosyalarını (Geotiff/ESW) listele"""
    print("🔍 Mevcut raster dosyaları (Geotiff/ESW) aranıyor...")
    
    search_dirs = ['test_images', 'dataset', '.']
    raster_extensions = ['.tif', '.tiff', '.geotiff', '.esw', '.ecw']
    
    found_files = []
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for root, dirs, files in os.walk(search_dir):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in raster_extensions):
                        full_path = os.path.join(root, file)
                        found_files.append(full_path)
    
    if found_files:
        print(f"📂 {len(found_files)} raster dosyası bulundu:")
        for i, file_path in enumerate(found_files, 1):
            print(f"   {i}. {file_path}")
    else:
        print("❌ Hiçbir raster dosyası bulunamadı.")
    
    return found_files

def interactive_test():
    """Interaktif test modu"""
    print("🎮 İnteraktif Test Modu")
    print("=" * 50)
    
    # Mevcut dosyaları listele
    raster_files = list_available_geotiffs()
    
    if not raster_files:
        print("\n💡 Raster dosyası eklemek için:")
        print("   1. test_images klasörüne raster dosyanızı (.tif/.esw) kopyalayın")
        print("   2. Veya run_geotiff_pipeline.py içinde test_geotiff yolunu güncelleyin")
        return
    
    # Kullanıcıdan seçim al
    print(f"\n📝 Test etmek istediğiniz dosyayı seçin (1-{len(raster_files)}):")
    
    try:
        choice = input("Seçiminiz: ").strip()
        if choice.isdigit():
            choice = int(choice)
            if 1 <= choice <= len(raster_files):
                selected_file = raster_files[choice - 1]
                print(f"\n🎯 Seçilen dosya: {selected_file}")
                
                # Test et
                test_with_file(selected_file)
            else:
                print("❌ Geçersiz seçim!")
        else:
            print("❌ Lütfen bir sayı girin!")
            
    except KeyboardInterrupt:
        print("\n👋 Test iptal edildi.")
    except Exception as e:
        print(f"❌ Hata: {e}")

def test_with_file(geotiff_path):
    """Belirtilen dosya ile test et"""
    try:
        print(f"🚀 Test başlatılıyor: {geotiff_path}")
        
        # Processor oluştur
        processor = GeotiffProcessor()
        
        # Pipeline'ı çalıştır
        results = processor.process_geotiff(geotiff_path)
        
        print("\n🎉 Test başarıyla tamamlandı!")
        print("📁 Sonuçlar:")
        for key, path in results.items():
            if os.path.exists(path):
                size = os.path.getsize(path) / (1024 * 1024)  # MB
                print(f"   ✅ {key}: {path} ({size:.1f} MB)")
            else:
                print(f"   ❌ {key}: {path} (dosya oluşturulamadı)")
                
    except Exception as e:
        print(f"❌ Test hatası: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Ana fonksiyon"""
    print("🌍 SAM-Adapter Raster Pipeline Test Aracı (Geotiff/ESW)")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        # Komut satırından dosya yolu verilmişse
        raster_path = sys.argv[1]
        if os.path.exists(raster_path):
            test_with_file(raster_path)
        else:
            print(f"❌ Dosya bulunamadı: {raster_path}")
    else:
        # İnteraktif mod
        interactive_test()

if __name__ == "__main__":
    main() 