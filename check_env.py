import rasterio
from osgeo import gdal

print(f"Rasterio version: {rasterio.__version__}")
print(f"GDAL version: {gdal.__version__}")

print("\n--- Available Drivers ---")
try:
    with rasterio.Env() as env:
        drivers = env.drivers()
        for driver_name in sorted(drivers.keys()):
            support = drivers[driver_name]
            print(f"- {driver_name}: {'read' if 'r' in support else ''}{'write' if 'w' in support else ''}")
        
        if 'ECW' in drivers:
            print("\n✅ ECW driver is available.")
        else:
            print("\n❌ ECW driver is NOT available.")

except Exception as e:
    print(f"\nError getting drivers: {e}") 