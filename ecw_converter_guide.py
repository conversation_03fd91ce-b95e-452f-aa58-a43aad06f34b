#!/usr/bin/env python3
"""
ECW Dosya Dönüştürme Kılavuzu
=============================

ECW (Enhanced Compression Wavelet) formatı özel bir sıkıştırma formatıdır
ve işlenmesi için özel driver'lar gerektirir.

Bu script ECW dosyalarını GeoTIFF formatına dönüştürme seçeneklerini sunar.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def print_banner():
    print("=" * 60)
    print("🔄 ECW → GeoTIFF Dönüştürücü Kılavuzu")
    print("=" * 60)

def check_gdal_installation():
    """GDAL kurulumunu kontrol et"""
    try:
        result = subprocess.run(['gdal_translate', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ GDAL kurulu:", result.stdout.strip())
            return True
    except:
        pass
    
    print("❌ GDAL komut satırı araçları bulunamadı")
    return False

def suggest_gdal_installation():
    """GDAL kurulum önerileri"""
    print("\n💡 GDAL Kurulum Seçenekleri:")
    print("   1. OSGeo4W (Windows): https://trac.osgeo.org/osgeo4w/")
    print("   2. QGIS ile birlikte gelir: https://qgis.org/")
    print("   3. Conda: conda install -c conda-forge gdal")
    print("   4. Docker: docker run --rm -v $(pwd):/data osgeo/gdal")

def convert_with_gdal(input_path, output_path):
    """GDAL ile ECW → GeoTIFF dönüştürme"""
    try:
        cmd = [
            'gdal_translate',
            '-of', 'GTiff',
            '-co', 'COMPRESS=LZW',
            '-co', 'TILED=YES',
            '-co', 'BIGTIFF=IF_SAFER',
            input_path,
            output_path
        ]
        
        print(f"🔧 Çalıştırılıyor: {' '.join(cmd)}")
        
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE, text=True)
        
        # Real-time output
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(f"   {output.strip()}")
        
        rc = process.poll()
        if rc == 0:
            print(f"✅ Dönüştürme başarılı: {output_path}")
            return True
        else:
            stderr = process.stderr.read()
            print(f"❌ Dönüştürme başarısız: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Hata: {e}")
        return False

def suggest_alternatives():
    """Alternatif dönüştürme yöntemleri"""
    print("\n🔄 Alternatif Dönüştürme Yöntemleri:")
    print("\n1. 🗺️  QGIS ile Dönüştürme:")
    print("   • QGIS'i açın")
    print("   • Layer → Add Layer → Add Raster Layer")
    print("   • ECW dosyasını seçin")
    print("   • Raster → Conversion → Translate (Convert Format)")
    print("   • Output format: GeoTIFF")
    print("   • Run")
    
    print("\n2. 🌐 Online Dönüştürücüler:")
    print("   • MyGeodata Converter: https://mygeodata.cloud/converter/")
    print("   • GDAL/OGR online: https://gdal.org/")
    print("   • AnyConv: https://anyconv.com/ecw-to-tiff-converter/")
    
    print("\n3. 🖥️  Masaüstü Uygulamaları:")
    print("   • Global Mapper")
    print("   • ArcGIS Desktop")
    print("   • ERDAS IMAGINE")
    print("   • FME Desktop")

def main():
    parser = argparse.ArgumentParser(description='ECW dosya dönüştürme kılavuzu')
    parser.add_argument('--input', type=str, help='ECW dosya yolu')
    parser.add_argument('--output', type=str, help='Çıktı GeoTIFF dosya yolu')
    parser.add_argument('--auto-convert', action='store_true', 
                       help='GDAL ile otomatik dönüştürme dene')
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.input:
        input_path = Path(args.input)
        if not input_path.exists():
            print(f"❌ Dosya bulunamadı: {input_path}")
            return
        
        if not input_path.suffix.lower() == '.ecw':
            print(f"⚠️  Bu bir ECW dosyası değil: {input_path}")
        
        print(f"📁 Giriş dosyası: {input_path}")
        print(f"📊 Dosya boyutu: {input_path.stat().st_size / (1024*1024):.1f} MB")
        
        # Çıktı dosya yolu
        if args.output:
            output_path = Path(args.output)
        else:
            output_path = input_path.with_suffix('.tif')
        
        print(f"📁 Çıktı dosyası: {output_path}")
        
        # GDAL ile dönüştürme dene
        if args.auto_convert:
            if check_gdal_installation():
                if convert_with_gdal(str(input_path), str(output_path)):
                    print(f"\n🎉 Dönüştürme tamamlandı!")
                    print(f"📌 Şimdi çalıştırabilirsiniz:")
                    print(f"   python geotiff_pipeline.py --input {output_path}")
                    return
            else:
                suggest_gdal_installation()
    
    # GDAL kurulum kontrolü
    has_gdal = check_gdal_installation()
    if not has_gdal:
        suggest_gdal_installation()
    
    # Alternatif yöntemler
    suggest_alternatives()
    
    print("\n" + "=" * 60)
    print("📌 Dönüştürme tamamlandıktan sonra:")
    print("   python geotiff_pipeline.py --input your_converted_file.tif")
    print("=" * 60)

if __name__ == "__main__":
    main() 