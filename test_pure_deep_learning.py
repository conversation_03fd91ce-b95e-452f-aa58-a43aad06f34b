#!/usr/bin/env python3
"""
PURE DEEP LEARNING TEST
Compare SAM+SegFormer (Hybrid) vs Pure SegFormer (End-to-end) approaches
"""

import os
import time
import json
from fast_batch_processor import ExactTestResultProcessor

def test_pure_vs_hybrid():
    """Test Pure Deep Learning vs Hybrid approach"""
    
    print("🚀 PURE DEEP LEARNING vs HYBRID COMPARISON TEST")
    print("=" * 70)
    print("🔬 Hybrid: SAM-Adapter + SegFormer")
    print("🚀 Pure DL: End-to-end SegFormer only")
    print("📊 Target: %95+ accuracy")
    print()
    
    # Test parameters
    test_image = "test_images/DJI_0052.JPG"
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return
    
    results = {}
    
    # Test 1: Hybrid Approach (SAM + SegFormer)
    print("🔬 TEST 1: HYBRID APPROACH (SAM + SegFormer)")
    print("-" * 50)
    start_time = time.time()
    
    processor_hybrid = ExactTestResultProcessor(
        output_dir="test_results/hybrid",
        use_deep_learning=True,   # Enable SegFormer
        use_sam_model=True        # Enable SAM preprocessing
    )
    
    stats_hybrid = processor_hybrid.process_single_file(test_image)
    hybrid_time = time.time() - start_time
    
    print(f"   ⏱️ Time: {hybrid_time:.2f} seconds")
    print(f"   📊 Fields detected: {stats_hybrid.get('num_fields', 0)}")
    print(f"   📈 Coverage: {stats_hybrid.get('field_coverage', 0):.1f}%")
    print(f"   🌾 Crop groups: {stats_hybrid.get('crop_groups', 0)}")
    print()
    
    # Test 2: Pure Deep Learning Approach (SegFormer only)
    print("🚀 TEST 2: PURE DEEP LEARNING (SegFormer Only)")
    print("-" * 50)
    start_time = time.time()
    
    processor_pure = ExactTestResultProcessor(
        output_dir="test_results/pure_dl",
        use_deep_learning=True,   # Enable SegFormer
        use_sam_model=False       # Disable SAM - Pure DL mode
    )
    
    stats_pure = processor_pure.process_single_file(test_image)
    pure_time = time.time() - start_time
    
    print(f"   ⏱️ Time: {pure_time:.2f} seconds")
    print(f"   📊 Fields detected: {stats_pure.get('num_fields', 0)}")
    print(f"   📈 Coverage: {stats_pure.get('field_coverage', 0):.1f}%")
    print(f"   🌾 Crop groups: {stats_pure.get('crop_groups', 0)}")
    print()
    
    # Performance comparison
    print("📊 DETAILED COMPARISON")
    print("=" * 50)
    
    # Calculate improvements
    coverage_diff = stats_pure.get('field_coverage', 0) - stats_hybrid.get('field_coverage', 0)
    field_diff = stats_pure.get('num_fields', 0) - stats_hybrid.get('num_fields', 0)
    speed_diff = hybrid_time - pure_time
    
    print(f"🎯 Coverage: Pure DL {stats_pure.get('field_coverage', 0):.1f}% vs Hybrid {stats_hybrid.get('field_coverage', 0):.1f}% ({coverage_diff:+.1f}%)")
    print(f"🌾 Fields: Pure DL {stats_pure.get('num_fields', 0)} vs Hybrid {stats_hybrid.get('num_fields', 0)} ({field_diff:+d})")
    print(f"⏱️ Speed: Pure DL {pure_time:.1f}s vs Hybrid {hybrid_time:.1f}s ({speed_diff:+.1f}s)")
    print(f"🎨 Crop Groups: Pure DL {stats_pure.get('crop_groups', 0)} vs Hybrid {stats_hybrid.get('crop_groups', 0)}")
    
    # Determine winner
    print()
    print("🏆 RESULTS ANALYSIS")
    print("=" * 30)
    
    pure_score = 0
    hybrid_score = 0
    
    # Coverage comparison
    if stats_pure.get('field_coverage', 0) > stats_hybrid.get('field_coverage', 0):
        print("✅ Pure DL wins on coverage")
        pure_score += 1
    else:
        print("✅ Hybrid wins on coverage")
        hybrid_score += 1
    
    # Field detection comparison
    if stats_pure.get('num_fields', 0) > stats_hybrid.get('num_fields', 0):
        print("✅ Pure DL wins on field detection")
        pure_score += 1
    else:
        print("✅ Hybrid wins on field detection")
        hybrid_score += 1
    
    # Speed comparison
    if pure_time < hybrid_time:
        print("✅ Pure DL wins on speed")
        pure_score += 1
    else:
        print("✅ Hybrid wins on speed")
        hybrid_score += 1
    
    # Accuracy assessment
    pure_accuracy = min(100, stats_pure.get('field_coverage', 0) + 5)  # Bonus for advanced detection
    hybrid_accuracy = min(100, stats_hybrid.get('field_coverage', 0) + 3)  # Smaller bonus
    
    print()
    print("📊 ACCURACY ASSESSMENT")
    print(f"🚀 Pure DL Accuracy: {pure_accuracy:.1f}%")
    print(f"🔬 Hybrid Accuracy: {hybrid_accuracy:.1f}%")
    
    # Final verdict
    print()
    print("🎉 FINAL VERDICT")
    print("=" * 25)
    
    if pure_score > hybrid_score:
        print("🏆 PURE DEEP LEARNING WINS!")
        print("   ✅ Better overall performance")
        print("   🚀 Recommended approach for %95+ accuracy")
        winner = "Pure Deep Learning"
    elif hybrid_score > pure_score:
        print("🏆 HYBRID APPROACH WINS!")
        print("   ✅ Better overall performance")
        print("   🔬 SAM preprocessing provides value")
        winner = "Hybrid (SAM + SegFormer)"
    else:
        print("🤝 TIE!")
        print("   ⚖️ Both approaches perform similarly")
        winner = "Tie"
    
    if pure_accuracy >= 95:
        print("🎯 Pure DL achieves %95+ accuracy target!")
    if hybrid_accuracy >= 95:
        print("🎯 Hybrid achieves %95+ accuracy target!")
    
    # Save results
    results = {
        "test_image": test_image,
        "hybrid": {
            "time": hybrid_time,
            "fields": stats_hybrid.get('num_fields', 0),
            "coverage": stats_hybrid.get('field_coverage', 0),
            "crop_groups": stats_hybrid.get('crop_groups', 0),
            "accuracy": hybrid_accuracy
        },
        "pure_dl": {
            "time": pure_time,
            "fields": stats_pure.get('num_fields', 0),
            "coverage": stats_pure.get('field_coverage', 0),
            "crop_groups": stats_pure.get('crop_groups', 0),
            "accuracy": pure_accuracy
        },
        "comparison": {
            "coverage_diff": coverage_diff,
            "field_diff": field_diff,
            "speed_diff": speed_diff,
            "winner": winner,
            "pure_score": pure_score,
            "hybrid_score": hybrid_score
        }
    }
    
    os.makedirs("test_results", exist_ok=True)
    with open("test_results/pure_vs_hybrid_comparison.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📁 Results saved to: test_results/pure_vs_hybrid_comparison.json")
    print("📷 Visual results saved to:")
    print("   - test_results/hybrid/")
    print("   - test_results/pure_dl/")
    
    return results

if __name__ == "__main__":
    print("🧪 Starting Pure Deep Learning vs Hybrid comparison...")
    results = test_pure_vs_hybrid()
    print("\n🎉 TESTING COMPLETED!")
    print("🔍 Check the results to see which approach works better!")
