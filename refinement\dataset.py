import os
import torch
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import numpy as np
from torchvision import transforms
import glob
import random

class BinaryRefinementDataset(Dataset):
    def __init__(self, sam_output_dir, ground_truth_dir, image_size=512, mode='train', use_augmentation=True):
        """
        High-performance Binary refinement dataset
        Args:
            sam_output_dir: SAM-adapter çıktılarının bulunduğu dizin
            ground_truth_dir: Ground truth binary mask'lerin bulunduğu dizin
            image_size: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> boyutu (daha büyük boyut = daha iyi performans)
            mode: 'train' or 'val'
            use_augmentation: Data augmentation kullanılsın mı
        """
        self.sam_output_dir = sam_output_dir
        self.ground_truth_dir = ground_truth_dir
        self.image_size = image_size
        self.mode = mode
        self.use_augmentation = use_augmentation and mode == 'train'
        
        # SAM çıktı dosyalarını bul
        self.sam_files = sorted(glob.glob(os.path.join(sam_output_dir, '*.jpg')))
        
        # Eşleşen ground truth dosyaları
        self.pairs = []
        for sam_file in self.sam_files:
            base_name = os.path.splitext(os.path.basename(sam_file))[0]
            gt_file = os.path.join(ground_truth_dir, base_name + '.png')
            
            if os.path.exists(gt_file):
                self.pairs.append((sam_file, gt_file))
        
        print(f"Found {len(self.pairs)} matching pairs for {mode}")
        
        # Transforms
        self.to_tensor = transforms.ToTensor()
        self.resize = transforms.Resize((image_size, image_size), interpolation=transforms.InterpolationMode.BILINEAR)
        self.resize_mask = transforms.Resize((image_size, image_size), interpolation=transforms.InterpolationMode.NEAREST)
        
        # Normalization (ImageNet standartları)
        self.normalize = transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
        
        # Augmentation transforms
        if self.use_augmentation:
            self.augment_transform = transforms.Compose([
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomVerticalFlip(p=0.3),
                transforms.RandomRotation(degrees=15),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.1, hue=0.05),
            ])
    
    def __len__(self):
        return len(self.pairs)
    
    def __getitem__(self, idx):
        sam_path, gt_path = self.pairs[idx]
        
        # SAM çıktısını yükle (gray scale)
        sam_img = Image.open(sam_path).convert('RGB')  # RGB'ye çevir
        sam_img = self.resize(sam_img)
        
        # Ground truth'u yükle (binary)
        gt_img = Image.open(gt_path).convert('L')  # Grayscale
        gt_img = self.resize_mask(gt_img)
        
        # Augmentation (train mode'da)
        if self.use_augmentation:
            # Aynı seed ile hem image hem mask'i augment et
            seed = random.randint(0, 2147483647)
            
            # SAM image augmentation
            random.seed(seed)
            torch.manual_seed(seed)
            sam_img = self.augment_transform(sam_img)
            
            # GT mask augmentation (renk değişikliği hariç)
            random.seed(seed)
            torch.manual_seed(seed)
            gt_augment = transforms.Compose([
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomVerticalFlip(p=0.3),
                transforms.RandomRotation(degrees=15),
            ])
            gt_img = gt_augment(gt_img)
        
        # Tensor'a çevir
        sam_tensor = self.to_tensor(sam_img)
        gt_tensor = self.to_tensor(gt_img)
        
        # SAM görüntüsünü normalize et
        sam_tensor = self.normalize(sam_tensor)
        
        # Ground truth'u binary yap (0 veya 1)
        gt_tensor = (gt_tensor > 0.5).float()
        
        return sam_tensor, gt_tensor

def get_dataloaders(sam_output_dir, ground_truth_dir, batch_size=8, image_size=256, num_workers=4):
    """
    Train ve validation dataloader'ları oluştur
    """
    # Dataset'leri oluştur
    full_dataset = BinaryRefinementDataset(
        sam_output_dir=sam_output_dir,
        ground_truth_dir=ground_truth_dir,
        image_size=image_size,
        mode='train',
        use_augmentation=True
    )
    
    # Train/Val split (80/20)
    train_size = int(0.8 * len(full_dataset))
    val_size = len(full_dataset) - train_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    # Val dataset için augmentation kapat
    val_dataset_no_aug = BinaryRefinementDataset(
        sam_output_dir=sam_output_dir,
        ground_truth_dir=ground_truth_dir,
        image_size=image_size,
        mode='val',
        use_augmentation=False
    )
    
    # Val indices'leri al
    val_indices = val_dataset.indices
    val_dataset_no_aug.pairs = [val_dataset_no_aug.pairs[i] for i in val_indices]
    
    # DataLoader'ları oluştur
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset_no_aug,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader 