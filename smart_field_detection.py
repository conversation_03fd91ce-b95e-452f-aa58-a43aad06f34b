#!/usr/bin/env python3
"""
Akıllı tarla tespiti - model çıktısını daha iyi analiz et
"""

import rasterio
import numpy as np
import matplotlib.pyplot as plt
import cv2
from skimage import filters, morphology, measure, segmentation
from scipy import ndimage
import os

def smart_field_detection():
    """Akıllı tarla tespiti"""
    
    print("🧠 Akıllı tarla tespiti başlıyor...")
    
    # Model çıktısını oku
    with rasterio.open("tiff_result/asd_center_5k_continuous.tif") as src:
        model_output = src.read(1)
        profile = src.profile
    
    # Orijinal görüntüyü de oku
    with rasterio.open("geotiff_input/asd_center_5k.tif") as src:
        original = src.read()
        if original.shape[0] <= 4:
            original = np.transpose(original, (1, 2, 0))
    
    print(f"📊 Model output: {model_output.shape}, range: {model_output.min():.3f} - {model_output.max():.3f}")
    
    # 1. Model çıktısının tersini al (düşük değerler = tarla sınırları)
    inverted_output = 1.0 - model_output
    print(f"📊 Inverted output range: {inverted_output.min():.3f} - {inverted_output.max():.3f}")
    
    # 2. Histogram analizi
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    
    # Orijinal
    if original.max() > 1:
        orig_vis = original / 255.0
    else:
        orig_vis = original
    axes[0, 0].imshow(orig_vis)
    axes[0, 0].set_title('Orijinal Görüntü')
    axes[0, 0].axis('off')
    
    # Model çıktısı
    axes[0, 1].imshow(model_output, cmap='viridis')
    axes[0, 1].set_title('Model Output')
    axes[0, 1].axis('off')
    
    # Ters model çıktısı
    axes[0, 2].imshow(inverted_output, cmap='viridis')
    axes[0, 2].set_title('Inverted Output\n(Düşük=Sınır)')
    axes[0, 2].axis('off')
    
    # Histogram
    axes[0, 3].hist(inverted_output.flatten(), bins=100, alpha=0.7, color='blue', label='Inverted')
    axes[0, 3].hist(model_output.flatten(), bins=100, alpha=0.7, color='red', label='Original')
    axes[0, 3].set_title('Histogram Karşılaştırması')
    axes[0, 3].legend()
    
    # 3. Percentile-based thresholding
    # En düşük %10'luk kısım tarla sınırları olabilir
    threshold_percentile = np.percentile(inverted_output, 90)  # En yüksek %10
    print(f"📊 90th percentile threshold: {threshold_percentile:.3f}")
    
    # Binary mask oluştur
    binary_mask = (inverted_output > threshold_percentile).astype(np.uint8) * 255
    boundary_ratio = np.sum(binary_mask > 127) / binary_mask.size
    print(f"📊 Boundary pixel ratio: {boundary_ratio:.3f}")
    
    axes[1, 0].imshow(binary_mask, cmap='gray')
    axes[1, 0].set_title(f'Percentile Binary\n({boundary_ratio:.3f} ratio)')
    axes[1, 0].axis('off')
    
    # 4. Otsu thresholding on inverted
    otsu_thresh = filters.threshold_otsu(inverted_output)
    binary_otsu = (inverted_output > otsu_thresh).astype(np.uint8) * 255
    otsu_ratio = np.sum(binary_otsu > 127) / binary_otsu.size
    print(f"📊 Otsu threshold: {otsu_thresh:.3f}, ratio: {otsu_ratio:.3f}")
    
    axes[1, 1].imshow(binary_otsu, cmap='gray')
    axes[1, 1].set_title(f'Otsu Binary\n({otsu_ratio:.3f} ratio)')
    axes[1, 1].axis('off')
    
    # 5. En iyi threshold'u seç
    if 0.05 <= boundary_ratio <= 0.25:  # Reasonable boundary ratio
        best_binary = binary_mask
        best_method = "Percentile"
        best_ratio = boundary_ratio
    elif 0.05 <= otsu_ratio <= 0.25:
        best_binary = binary_otsu
        best_method = "Otsu"
        best_ratio = otsu_ratio
    else:
        # Manual threshold
        manual_thresh = 0.7  # Try different values
        best_binary = (inverted_output > manual_thresh).astype(np.uint8) * 255
        best_method = f"Manual ({manual_thresh})"
        best_ratio = np.sum(best_binary > 127) / best_binary.size
    
    print(f"✅ En iyi method: {best_method}, ratio: {best_ratio:.3f}")
    
    # 6. Morphological operations
    kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
    
    # Clean boundaries
    cleaned = cv2.morphologyEx(best_binary, cv2.MORPH_CLOSE, kernel_small, iterations=2)
    cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_small, iterations=1)
    
    axes[1, 2].imshow(cleaned, cmap='gray')
    axes[1, 2].set_title(f'Cleaned Boundaries\n({best_method})')
    axes[1, 2].axis('off')
    
    # 7. Invert to get field regions (not boundaries)
    field_regions = 255 - cleaned  # Invert: boundaries -> fields
    
    # Fill holes and clean
    field_regions = cv2.morphologyEx(field_regions, cv2.MORPH_CLOSE, kernel_medium, iterations=3)
    field_regions = cv2.morphologyEx(field_regions, cv2.MORPH_OPEN, kernel_medium, iterations=2)
    
    axes[1, 3].imshow(field_regions, cmap='gray')
    axes[1, 3].set_title('Field Regions')
    axes[1, 3].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/smart_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 8. Connected components on field regions
    num_labels, labels = cv2.connectedComponents(field_regions)
    print(f"📊 Toplam field bileşen sayısı: {num_labels-1}")
    
    # Area filtering for fields
    min_field_area = 5000   # Minimum field size
    max_field_area = 1000000  # Maximum field size
    
    filtered_labels = np.zeros_like(labels)
    valid_areas = []
    label_counter = 1
    
    for label_id in range(1, num_labels):
        mask = (labels == label_id)
        area = np.sum(mask)
        
        if min_field_area <= area <= max_field_area:
            filtered_labels[mask] = label_counter
            valid_areas.append(area)
            label_counter += 1
            print(f"   Field {label_counter-1}: {area} pixels")
    
    field_count = label_counter - 1
    print(f"✅ {field_count} geçerli tarla bulundu")
    
    if valid_areas:
        print(f"📊 Alan dağılımı: {np.min(valid_areas)} - {np.max(valid_areas)} pixels")
        print(f"📊 Ortalama alan: {np.mean(valid_areas):.0f} pixels")
    
    # 9. Final visualization
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Original
    axes[0, 0].imshow(orig_vis)
    axes[0, 0].set_title('Orijinal Görüntü')
    axes[0, 0].axis('off')
    
    # Detected boundaries
    axes[0, 1].imshow(cleaned, cmap='gray')
    axes[0, 1].set_title(f'Tespit Edilen Sınırlar\n({best_method})')
    axes[0, 1].axis('off')
    
    # Field regions
    axes[1, 0].imshow(field_regions, cmap='gray')
    axes[1, 0].set_title('Tarla Bölgeleri')
    axes[1, 0].axis('off')
    
    # Colored fields
    colored_fields = np.zeros((filtered_labels.shape[0], filtered_labels.shape[1], 3), dtype=np.uint8)
    
    if field_count > 0:
        # Generate distinct colors
        colors = plt.cm.Set3(np.linspace(0, 1, field_count))[:, :3] * 255
        
        for label_id in range(1, field_count + 1):
            mask = (filtered_labels == label_id)
            if label_id <= len(colors):
                colored_fields[mask] = colors[label_id-1]
    
    axes[1, 1].imshow(colored_fields)
    axes[1, 1].set_title(f'Renkli Tarlalar\n({field_count} tarla)')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/smart_field_result.png', dpi=150, bbox_inches='tight')
    print("💾 Akıllı tespit kaydedildi: tiff_result/smart_field_result.png")
    plt.show()
    
    # 10. Save results
    # Boundaries
    boundary_profile = profile.copy()
    boundary_profile.update(dtype='uint8', count=1)
    
    with rasterio.open("tiff_result/asd_center_5k_boundaries.tif", 'w', **boundary_profile) as dst:
        dst.write(cleaned, 1)
    
    # Field regions
    with rasterio.open("tiff_result/asd_center_5k_field_regions.tif", 'w', **boundary_profile) as dst:
        dst.write(field_regions, 1)
    
    # Field labels
    labels_profile = profile.copy()
    labels_profile.update(dtype='uint16', count=1)
    
    with rasterio.open("tiff_result/asd_center_5k_smart_fields.tif", 'w', **labels_profile) as dst:
        dst.write(filtered_labels.astype(np.uint16), 1)
    
    # Colored visualization
    rgb_profile = profile.copy()
    rgb_profile.update(dtype='uint8', count=3)
    
    with rasterio.open("tiff_result/asd_center_5k_smart_colored.tif", 'w', **rgb_profile) as dst:
        for i in range(3):
            dst.write(colored_fields[:, :, i], i+1)
    
    print("💾 Akıllı sonuçlar kaydedildi:")
    print("   - asd_center_5k_boundaries.tif (tarla sınırları)")
    print("   - asd_center_5k_field_regions.tif (tarla bölgeleri)")
    print("   - asd_center_5k_smart_fields.tif (tarla etiketleri)")
    print("   - asd_center_5k_smart_colored.tif (renkli görselleştirme)")
    
    return filtered_labels, colored_fields, field_count

if __name__ == "__main__":
    # Akıllı tarla tespiti
    labels, colored, count = smart_field_detection()
    
    print(f"✅ Akıllı tespit tamamlandı! {count} tarla bulundu.")
