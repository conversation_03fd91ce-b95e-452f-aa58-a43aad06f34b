import argparse
import os

import yaml
import torch
from torch.utils.data import DataLoader
from tqdm import tqdm

import datasets
import models
import utils

from torchvision import transforms
from mmcv.runner import load_checkpoint
import numpy as np
from PIL import Image


def batched_predict(model, inp, coord, bsize):
    with torch.no_grad():
        model.gen_feat(inp)
        n = coord.shape[1]
        ql = 0
        preds = []
        while ql < n:
            qr = min(ql + bsize, n)
            pred = model.query_rgb(coord[:, ql: qr, :])
            preds.append(pred)
            ql = qr
        pred = torch.cat(preds, dim=1)
    return pred, preds


def tensor2PIL(tensor):
    toPIL = transforms.ToPILImage()
    return toPIL(tensor)


device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


def eval_psnr(loader, model, data_norm=None, eval_type=None, eval_bsize=None,
              verbose=False, save_binary_outputs=False, output_dir='test_results', 
              threshold=0.5):
    model.eval()
    if data_norm is None:
        data_norm = {
            'inp': {'sub': [0], 'div': [1]},
            'gt': {'sub': [0], 'div': [1]}
        }

    if eval_type == 'f1':
        metric_fn = utils.calc_f1
        metric1, metric2, metric3, metric4 = 'f1', 'auc', 'none', 'none'
    elif eval_type == 'fmeasure':
        metric_fn = utils.calc_fmeasure
        metric1, metric2, metric3, metric4 = 'f_mea', 'mae', 'none', 'none'
    elif eval_type == 'ber':
        metric_fn = utils.calc_ber
        metric1, metric2, metric3, metric4 = 'shadow', 'non_shadow', 'ber', 'none'
    elif eval_type == 'cod':
        metric_fn = utils.calc_cod
        metric1, metric2, metric3, metric4 = 'sm', 'em', 'wfm', 'mae'

    val_metric1 = utils.Averager()
    val_metric2 = utils.Averager()
    val_metric3 = utils.Averager()
    val_metric4 = utils.Averager()

    pbar = tqdm(loader, leave=False, desc='val')

    # CUDA belleğini temizle
    torch.cuda.empty_cache()

    # Binary çıktılar için klasör oluştur
    if save_binary_outputs:
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'binary'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'continuous'), exist_ok=True)

    image_count = 0
    with torch.no_grad():  # Gradient hesaplamayı devre dışı bırak
        for batch in pbar:
            # Her 10 batch'te bir belleği temizle
            if pbar.n % 10 == 0:
                torch.cuda.empty_cache()

            for k, v in batch.items():
                batch[k] = v.cuda()

            inp = batch['inp']

            # Modelin çıktısını al (sigmoid öncesi)
            pred_continuous = torch.sigmoid(model.infer(inp))
            
            # Binary threshold uygula
            pred_binary = (pred_continuous > threshold).float()

            # Metrik hesaplamaları için binary çıktıyı kullan
            result1, result2, result3, result4 = metric_fn(pred_binary, batch['gt'])
            val_metric1.add(result1.item(), inp.shape[0])
            val_metric2.add(result2.item(), inp.shape[0])
            val_metric3.add(result3.item(), inp.shape[0])
            val_metric4.add(result4.item(), inp.shape[0])

            # Görselleştirme için çıktıları kaydet
            if save_binary_outputs:
                for i in range(pred_continuous.shape[0]):
                    # Continuous çıktıyı kaydet (gri tonlamalı)
                    continuous_img = (pred_continuous[i, 0].cpu().numpy() * 255).astype(np.uint8)
                    continuous_pil = Image.fromarray(continuous_img, mode='L')
                    continuous_pil.save(os.path.join(output_dir, 'continuous', f'image_{image_count:04d}_continuous.png'))
                    
                    # Binary çıktıyı kaydet (siyah-beyaz)
                    binary_img = (pred_binary[i, 0].cpu().numpy() * 255).astype(np.uint8)
                    binary_pil = Image.fromarray(binary_img, mode='L')
                    binary_pil.save(os.path.join(output_dir, 'binary', f'image_{image_count:04d}_binary.png'))
                    
                    image_count += 1

            # Geçici değişkenleri temizle
            del inp, pred_continuous, pred_binary, batch
            torch.cuda.empty_cache()

            if verbose:
                pbar.set_description('val {} {:.4f}, {} {:.4f}, {} {:.4f}, {} {:.4f}'.format(
                    metric1, val_metric1.item(),
                    metric2, val_metric2.item(),
                    metric3, val_metric3.item(),
                    metric4, val_metric4.item()
                ))

    # Son bir kez belleği temizle
    torch.cuda.empty_cache()

    return val_metric1.item(), val_metric2.item(), val_metric3.item(), val_metric4.item()


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--config')
    parser.add_argument('--model')
    parser.add_argument('--prompt', default='none')
    parser.add_argument('--save_outputs', action='store_true', help='Siyah-beyaz çıktıları kaydet')
    parser.add_argument('--output_dir', default='test_results', help='Çıktı klasörü')
    parser.add_argument('--threshold', type=float, default=0.5, help='Binary threshold değeri')
    args = parser.parse_args()

    with open(args.config, 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    spec = config['test_dataset']
    dataset = datasets.make(spec['dataset'])
    dataset = datasets.make(spec['wrapper'], args={'dataset': dataset})
    loader = DataLoader(dataset, batch_size=spec['batch_size'],
                        num_workers=8)

    # CUDA belleğini temizle
    torch.cuda.empty_cache()

    # Modeli oluştur
    model = models.make(config['model']).cuda()

    # Checkpoint'i CPU'ya yükle
    print(f"Loading model from {args.model}")
    sam_checkpoint = torch.load(args.model, map_location='cpu')

    # Model durumunu yükle
    try:
        model.load_state_dict(sam_checkpoint, strict=True)
        print("Model successfully loaded")
    except Exception as e:
        print(f"Error loading model: {e}")
        import traceback
        traceback.print_exc()

    # CUDA belleğini tekrar temizle
    torch.cuda.empty_cache()

    metric1, metric2, metric3, metric4 = eval_psnr(loader, model,
                                                   data_norm=config.get('data_norm'),
                                                   eval_type=config.get('eval_type'),
                                                   eval_bsize=config.get('eval_bsize'),
                                                   verbose=True,
                                                   save_binary_outputs=args.save_outputs,
                                                   output_dir=args.output_dir,
                                                   threshold=args.threshold)
    print('metric1: {:.4f}'.format(metric1))
    print('metric2: {:.4f}'.format(metric2))
    print('metric3: {:.4f}'.format(metric3))
    print('metric4: {:.4f}'.format(metric4))
    
    if args.save_outputs:
        print(f"Binary çıktılar {args.output_dir} klasörüne kaydedildi")
        print(f"Threshold değeri: {args.threshold}")
