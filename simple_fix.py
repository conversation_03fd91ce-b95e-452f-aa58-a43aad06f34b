#!/usr/bin/env python3
"""
Basit ve hızlı çözüm
"""

import rasterio
import numpy as np
import matplotlib.pyplot as plt
import cv2

def simple_field_detection():
    """Basit ve etkili tarla tespiti"""
    
    print("⚡ Basit tarla tespiti...")
    
    # Model çıktısını oku
    with rasterio.open("tiff_result/asd_center_5k_continuous.tif") as src:
        model_output = src.read(1)
        profile = src.profile
    
    # Orijinal görüntüyü oku
    with rasterio.open("geotiff_input/asd_center_5k.tif") as src:
        original = src.read()
        if original.shape[0] <= 4:
            original = np.transpose(original, (1, 2, 0))
    
    print(f"📊 Model range: {model_output.min():.3f} - {model_output.max():.3f}")
    
    # Model çıktısının tersini al (düşük değerler = sınırlar)
    inverted = 1.0 - model_output
    
    # Çok agresif threshold - sadece en düşük %5'lik kısım
    threshold = np.percentile(inverted, 95)
    print(f"📊 95th percentile threshold: {threshold:.3f}")
    
    # Binary sınırlar
    boundaries = (inverted > threshold).astype(np.uint8) * 255
    boundary_ratio = np.sum(boundaries > 127) / boundaries.size
    print(f"📊 Boundary ratio: {boundary_ratio:.3f}")
    
    # Sınırları temizle
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    boundaries_clean = cv2.morphologyEx(boundaries, cv2.MORPH_CLOSE, kernel, iterations=2)
    boundaries_clean = cv2.morphologyEx(boundaries_clean, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # Tarla bölgelerini al (sınırların tersi)
    fields = 255 - boundaries_clean
    
    # Tarla bölgelerini temizle
    kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
    fields_clean = cv2.morphologyEx(fields, cv2.MORPH_CLOSE, kernel_large, iterations=3)
    fields_clean = cv2.morphologyEx(fields_clean, cv2.MORPH_OPEN, kernel_large, iterations=2)
    
    # Connected components
    num_labels, labels = cv2.connectedComponents(fields_clean)
    print(f"📊 Toplam bileşen: {num_labels-1}")
    
    # Alan filtresi
    min_area = 10000  # 10K pixels minimum
    max_area = 2000000  # 2M pixels maximum
    
    filtered_labels = np.zeros_like(labels)
    field_count = 0
    
    for label_id in range(1, num_labels):
        mask = (labels == label_id)
        area = np.sum(mask)
        
        if min_area <= area <= max_area:
            field_count += 1
            filtered_labels[mask] = field_count
            print(f"   Field {field_count}: {area} pixels")
    
    print(f"✅ {field_count} tarla bulundu")
    
    # Görselleştirme
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Orijinal
    if original.max() > 1:
        orig_vis = original / 255.0
    else:
        orig_vis = original
    axes[0, 0].imshow(orig_vis)
    axes[0, 0].set_title('Orijinal Görüntü')
    axes[0, 0].axis('off')
    
    # Model çıktısı
    axes[0, 1].imshow(model_output, cmap='viridis')
    axes[0, 1].set_title('Model Output')
    axes[0, 1].axis('off')
    
    # Ters model
    axes[0, 2].imshow(inverted, cmap='viridis')
    axes[0, 2].set_title('Inverted (Düşük=Sınır)')
    axes[0, 2].axis('off')
    
    # Sınırlar
    axes[1, 0].imshow(boundaries_clean, cmap='gray')
    axes[1, 0].set_title(f'Sınırlar ({boundary_ratio:.3f})')
    axes[1, 0].axis('off')
    
    # Tarla bölgeleri
    axes[1, 1].imshow(fields_clean, cmap='gray')
    axes[1, 1].set_title('Tarla Bölgeleri')
    axes[1, 1].axis('off')
    
    # Renkli tarlalar
    colored_fields = np.zeros((filtered_labels.shape[0], filtered_labels.shape[1], 3), dtype=np.uint8)
    
    if field_count > 0:
        colors = plt.cm.Set3(np.linspace(0, 1, field_count))[:, :3] * 255
        
        for label_id in range(1, field_count + 1):
            mask = (filtered_labels == label_id)
            if label_id <= len(colors):
                colored_fields[mask] = colors[label_id-1]
    
    axes[1, 2].imshow(colored_fields)
    axes[1, 2].set_title(f'Tarlalar ({field_count} adet)')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/simple_result.png', dpi=150, bbox_inches='tight')
    print("💾 Basit sonuç kaydedildi: tiff_result/simple_result.png")
    plt.show()
    
    # Karşılaştırma
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    
    axes[0].imshow(orig_vis)
    axes[0].set_title('Orijinal Görüntü\n(Net tarla sınırları görülüyor)')
    axes[0].axis('off')
    
    axes[1].imshow(colored_fields)
    axes[1].set_title(f'SAM-Adapter Sonucu\n({field_count} tarla tespit edildi)')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig('tiff_result/final_comparison.png', dpi=150, bbox_inches='tight')
    print("💾 Final karşılaştırma: tiff_result/final_comparison.png")
    plt.show()
    
    # Sonuçları kaydet
    # Sınırlar
    boundary_profile = profile.copy()
    boundary_profile.update(dtype='uint8', count=1)
    
    with rasterio.open("tiff_result/final_boundaries.tif", 'w', **boundary_profile) as dst:
        dst.write(boundaries_clean, 1)
    
    # Tarla etiketleri
    labels_profile = profile.copy()
    labels_profile.update(dtype='uint16', count=1)
    
    with rasterio.open("tiff_result/final_fields.tif", 'w', **labels_profile) as dst:
        dst.write(filtered_labels.astype(np.uint16), 1)
    
    # Renkli görselleştirme
    rgb_profile = profile.copy()
    rgb_profile.update(dtype='uint8', count=3)
    
    with rasterio.open("tiff_result/final_colored.tif", 'w', **rgb_profile) as dst:
        for i in range(3):
            dst.write(colored_fields[:, :, i], i+1)
    
    print("💾 Final sonuçlar kaydedildi:")
    print("   - final_boundaries.tif")
    print("   - final_fields.tif") 
    print("   - final_colored.tif")
    
    return field_count

if __name__ == "__main__":
    count = simple_field_detection()
    print(f"✅ Basit tespit tamamlandı! {count} tarla bulundu.")
    
    print("\n🔍 SORUN ANALİZİ:")
    print("1. Model çıktısı çok yüksek değerler veriyor (0.8+ ratio)")
    print("2. Bu, modelin tarla içlerini yüksek, sınırları düşük değer verdiği anlamına geliyor")
    print("3. Threshold'u tersine çevirmek gerekiyor")
    print("4. Daha agresif post-processing gerekli")
    
    print("\n💡 ÖNERİLER:")
    print("1. Model eğitimini kontrol edin - ground truth doğru mu?")
    print("2. Loss function'ı gözden geçirin")
    print("3. Farklı threshold stratejileri deneyin")
    print("4. Model architecture'ı optimize edin")
