model:
  name: sam
  args:
    inp_size: 1024
    encoder_mode:
      name: sam
      args:
        inp_size: 1024
        model_type: 'vit_h'
        checkpoint: './pretrained/sam_vit_h_4b8939.pth'
        pixel_mean: [123.675, 116.28, 103.53]
        pixel_std: [58.395, 57.12, 57.375]
    decoder_mode:
      name: ham
      args:
        inp_size: 1024
        embed_dim: 256
        patch_size: 16
        num_heads: 8
        depth: 2

test_dataset:
  dataset:
    name: image-folder
    args:
      root_path: ./test_images
      inp_size: 1024
  wrapper:
    name: test-time
    args:
      inp_size: 1024
  batch_size: 1

eval_type: f1
data_norm:
  inp: {sub: [0.485, 0.456, 0.406], div: [0.229, 0.224, 0.225]}
  gt: {sub: [0], div: [1]} 