# 🌍 SAM-Adapter Geotiff İşleme Pipeline'ı

Bu pipeline, büyük raster dosyalarını (Geotiff: .tif/.tiff, ESW: .esw) SAM-Adapter modeli ile işlemek için özel olarak tasarlanmıştır. İstediğiniz şekilde:

1. **1024x1024 pencere kaydırma** ile model inference
2. **512x512 padding** ile ikinci tur işleme  
3. **Simple Otsu Direct** metodunu uygulama
4. Sonuçları **tiff_result** klasörüne kaydetme

## 🚀 Hızlı Başlangıç

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON>

```bash
pip install -r requirements.txt
```

**Yeni eklenen geotiff kütüphaneleri:**
- `rasterio>=1.3.0` - Geotiff okuma/yazma
- `geopandas>=0.12.0` - Coğrafi veri işleme

### 2. Model Dosyaları

Pipeline'ın çalı<PERSON>ması için şu dosyaların mevcut olması gerekir:

```
├── configs/cod-sam-vit-h.yaml      # Model konfigürasyonu
├── save/checkpoint_14.pth          # Eğitilmiş model ağırlıkları
└── pretrained/sam_vit_h_4b8939.pth # SAM base model
```

### 3. Kullanım

#### Komut Satırından:

```bash
# Basit kullanım
python geotiff_pipeline.py --input your_raster_file.tif
# veya ESW dosyası için
python geotiff_pipeline.py --input your_esw_file.esw

# Özel config ile
python geotiff_pipeline.py --input your_raster_file.tif --config configs/cod-sam-vit-h.yaml --checkpoint save/checkpoint_14.pth
```

#### Test Script'i ile:

```bash
# İnteraktif test
python run_geotiff_pipeline.py

# Belirli dosya ile test
python run_geotiff_pipeline.py your_raster_file.tif
# veya 
python run_geotiff_pipeline.py your_esw_file.esw
```

## 🔧 Pipeline İşlem Adımları

### 1. Raster Dosya Yükleme
- Rasterio ile raster dosyası (.tif/.tiff/.esw) okunur
- Metadata (CRS, transform) korunur

### 2. İlk İşleme (1024x1024 Window)
- 512x512 padding eklenir
- 1024x1024 pencereler oluşturulur (%50 overlap)
- Her pencere SAM-Adapter ile işlenir
- Sonuçlar birleştirilir

### 3. İkinci İşleme (Refined Padding)
- Farklı offset ile pencereler oluşturulur
- Daha fazla overlap (%75) ile yeniden işlenir
- Sonuçlar birleştirilir

### 4. Sonuç Birleştirme
- İki adımın sonuçları ortalaması alınır
- Daha robust ve kararlı sonuç elde edilir

### 5. Simple Otsu Direct
- 32x32 sliding window ile Otsu threshold
- %50 overlap ile threshold map oluşturulur
- Binary mask üretilir
- Morphological temizlik uygulanır

### 6. Sonuç Kaydetme
- **Continuous sonuç**: Model çıktısı (0-255)
- **Binary sonuç**: Otsu threshold uygulanmış
- **Threshold map**: Otsu threshold değerleri
- **Görselleştirme**: 2x2 grid karşılaştırma

## 📁 Çıktı Dosyaları

Pipeline çalıştırıldıktan sonra `tiff_result/` klasöründe şu dosyalar oluşturulur:

```
tiff_result/
├── filename_continuous.tif    # Model çıktısı (gri tonlamalı)
├── filename_binary.tif        # Binary sonuç (siyah-beyaz)
├── filename_threshold.tif     # Threshold haritası
└── filename_visualization.png # Görsel karşılaştırma
```

### Dosya Özellikleri:
- **Format**: GeoTIFF (LZW sıkıştırma)
- **Koordinat sistemi**: Orijinal dosya ile aynı
- **Çözünürlük**: Orijinal dosya ile aynı
- **Veri tipi**: uint8 (0-255)

## ⚙️ Konfigürasyon

### Pipeline Parametreleri

`GeotiffProcessor` sınıfında özelleştirilebilir parametreler:

```python
processor = GeotiffProcessor(
    config_path='configs/cod-sam-vit-h.yaml',
    checkpoint_path='save/checkpoint_14.pth'
)

# Özelleştirilebilir parametreler:
processor.window_size = 1024      # Window boyutu
processor.padding_size = 512      # Padding boyutu  
processor.output_dir = 'tiff_result'  # Çıktı klasörü
```

### Simple Otsu Parametreleri

```python
# _apply_simple_otsu_direct metodunda:
window_size = 32    # Otsu window boyutu
step_size = 16      # %50 overlap
```

## 🎯 Kullanım Örnekleri

### Örnek 1: Basit Kullanım

```python
from geotiff_pipeline import GeotiffProcessor

# Processor oluştur
processor = GeotiffProcessor()

# Geotiff işle
results = processor.process_geotiff('my_satellite_image.tif')

# Sonuçları kontrol et
print("Oluşturulan dosyalar:")
for key, path in results.items():
    print(f"- {key}: {path}")
```

### Örnek 2: Özel Konfigürasyon

```python
# Özel model ile
processor = GeotiffProcessor(
    config_path='configs/custom-config.yaml',
    checkpoint_path='save/custom_checkpoint.pth'
)

# Özel çıktı klasörü
processor.output_dir = 'my_results'

# İşle
results = processor.process_geotiff('large_geotiff.tif')
```

### Örnek 3: Batch İşleme

```python
import glob

processor = GeotiffProcessor()

# Klasördeki tüm geotiff dosyalarını işle
geotiff_files = glob.glob('input_folder/*.tif')

for geotiff_file in geotiff_files:
    print(f"İşleniyor: {geotiff_file}")
    try:
        results = processor.process_geotiff(geotiff_file)
        print("✅ Başarılı")
    except Exception as e:
        print(f"❌ Hata: {e}")
```

## 🔍 Performans ve Optimizasyon

### Bellek Kullanımı
- GPU belleği her 10 window'da bir temizlenir
- Büyük dosyalar için çok fazla RAM kullanabilir
- 16GB+ RAM önerilir

### İşlem Süresi
- Dosya boyutuna bağlı olarak değişir
- 1000x1000 pixel: ~2-5 dakika
- 5000x5000 pixel: ~15-30 dakika
- GPU kullanan işlemler paralel değil (sequence)

### Optimizasyon İpuçları
1. **GPU belleği**: Büyük dosyalar için batch size azaltın
2. **Disk alanı**: LZW sıkıştırma kullanılır ama yine de çıktılar büyük olabilir
3. **İşlem süresi**: Overlap oranını azaltarak hızlandırabilirsiniz

## 🐛 Sorun Giderme

### Yaygın Hatalar

#### 1. Model dosyası bulunamadı
```
❌ Hata: save/checkpoint_14.pth dosyası bulunamadı!
```
**Çözüm**: Model checkpoint dosyasının doğru yolda olduğunu kontrol edin.

#### 2. CUDA bellek hatası
```
RuntimeError: CUDA out of memory
```
**Çözüm**: 
- Daha küçük window size kullanın
- GPU belleğini temizleyin: `torch.cuda.empty_cache()`

#### 3. Geotiff okuma hatası
```
rasterio.errors.RasterioIOError
```
**Çözüm**: 
- Dosya yolunu kontrol edin
- Dosya formatının desteklendiğini kontrol edin
- Dosya izinlerini kontrol edin

#### 4. Padding hatası
```
ValueError: array dimensions mismatch
```
**Çözüm**: Çok küçük geotiff dosyaları için padding_size değerini azaltın.

### Debug Modu

Detaylı hata bilgisi için:

```python
import traceback

try:
    processor = GeotiffProcessor()
    results = processor.process_geotiff('test.tif')
except Exception as e:
    print(f"Hata: {e}")
    traceback.print_exc()
```

## 📊 Sonuç Analizi

### Görselleştirme

Pipeline otomatik olarak bir görselleştirme oluşturur:

1. **Orijinal geotiff**: Giriş görüntüsü
2. **SAM-Adapter çıktısı**: Model confidence değerleri
3. **Threshold haritası**: Otsu threshold değerleri  
4. **Binary sonuç**: Final siyah-beyaz mask

### Kalite Kontrol

Sonuçların kalitesini kontrol etmek için:

```python
import cv2
import numpy as np

# Binary sonucu yükle
binary = cv2.imread('tiff_result/filename_binary.tif', 0)

# İstatistikler
coverage = (binary > 127).sum() / binary.size * 100
print(f"Kapsama oranı: %{coverage:.1f}")

# Bağlı bileşen analizi
num_labels, labels = cv2.connectedComponents(binary)
print(f"Bağlı bileşen sayısı: {num_labels - 1}")
```

## 🔄 Pipeline Genişletme

### Yeni Post-processing Adımları

```python
def custom_postprocess(self, binary_mask):
    """Özel post-processing adımı"""
    # Örnek: Small objects removal
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    cleaned = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)
    return cleaned

# GeotiffProcessor sınıfına ekle
GeotiffProcessor.custom_postprocess = custom_postprocess
```

### Farklı Threshold Metodları

```python
def adaptive_threshold(self, prediction):
    """Adaptive threshold metodunu uygula"""
    # Gaussian adaptive threshold
    pred_uint8 = (prediction * 255).astype(np.uint8)
    binary = cv2.adaptiveThreshold(pred_uint8, 255, 
                                   cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY, 11, 2)
    return binary
```

## 🤝 Katkıda Bulunma

Pipeline'ı geliştirmek için:

1. Yeni özellikler ekleyin
2. Bug'ları düzeltin  
3. Performans optimizasyonları yapın
4. Dokümantasyonu güncelleyin

## 📞 Destek

Sorularınız için:
- Issue açın
- Kod örnekleri sağlayın
- Hata mesajlarını tam olarak paylaşın

---

**🎉 Pipeline'ınız hazır! Geotiff dosyalarınızı işlemeye başlayabilirsiniz.** 

## Desteklenen Formatlar

Pipeline aşağıdaki raster formatlarını destekler:

- **GeoTIFF (.tif, .tiff)** - Tam destek ✅
- **ESW (.esw)** - Tam destek ✅  
- **ECW (.ecw)** - Dönüştürme gerekli ⚠️

### ECW Dosya Desteği

ECW (Enhanced Compression Wavelet) formatı özel bir sıkıştırma formatıdır ve işlenmesi için özel driver'lar gerektirir. Pipeline ECW dosyalarını otomatik olarak tespit eder ve dönüştürme kılavuzunu çalıştırır.

**ECW dosyası tespit edildiğinde:**
```bash
python geotiff_pipeline.py --input your_file.ecw
# Otomatik olarak dönüştürme kılavuzu çalışır
```

**Manuel dönüştürme kılavuzu:**
```bash
python ecw_converter_guide.py --input your_file.ecw
```

**Dönüştürme seçenekleri:**
1. **QGIS** (Önerilen):
   - QGIS'i açın
   - Layer → Add Layer → Add Raster Layer → ECW dosyasını seçin
   - Raster → Conversion → Translate (Convert Format)
   - Output format: GeoTIFF

2. **Online Dönüştürücüler**:
   - [MyGeodata Converter](https://mygeodata.cloud/converter/)
   - [AnyConv ECW to TIFF](https://anyconv.com/ecw-to-tiff-converter/)

3. **Masaüstü Uygulamaları**:
   - Global Mapper, ArcGIS Desktop, ERDAS IMAGINE

**Dönüştürme sonrası:**
```bash
python geotiff_pipeline.py --input converted_file.tif
``` 