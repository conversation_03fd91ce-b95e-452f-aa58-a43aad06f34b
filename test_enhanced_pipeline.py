#!/usr/bin/env python3
"""
Enhanced GeoTIFF Pipeline Test Script
Gelişmiş tarla segmentasyonu ve renklendirme testi
"""

import os
import sys
import argparse
from geotiff_pipeline import GeotiffProcessor

def test_enhanced_pipeline():
    """Enhanced pipeline'ı test et"""
    
    # Test parametreleri
    test_configs = [
        {
            'name': 'Küçük Renk Paleti',
            'num_colors': 8,
            'description': '8 farklı renk ile büyük tarla grupları'
        },
        {
            'name': 'Orta Renk Paleti', 
            'num_colors': 24,
            'description': '24 farklı renk ile detaylı segmentasyon'
        },
        {
            'name': 'Büyük Renk Paleti',
            'num_colors': 64,
            'description': '64 farklı renk ile çok detaylı segmentasyon'
        }
    ]
    
    # Test dosyası kontrolü
    test_files = [
        'test_data/sample.tif',
        'test_data/sample.tiff', 
        'data/test.tif',
        'example.tif'
    ]
    
    input_file = None
    for test_file in test_files:
        if os.path.exists(test_file):
            input_file = test_file
            break
    
    if not input_file:
        print("❌ Test dosyası bulunamadı!")
        print("📝 Test için aşağıdaki konumlardan birinde GeoTIFF dosyası bulunmalı:")
        for tf in test_files:
            print(f"   - {tf}")
        return False
    
    print(f"✅ Test dosyası bulundu: {input_file}")
    
    # Her konfigürasyon için test
    for config in test_configs:
        print(f"\n🧪 Test: {config['name']}")
        print(f"📝 {config['description']}")
        print(f"🎨 Renk sayısı: {config['num_colors']}")
        
        try:
            # Processor oluştur
            processor = GeotiffProcessor(
                config_path='configs/cod-sam-vit-h.yaml',
                checkpoint_path='save/checkpoint_14.pth',
                num_colors=config['num_colors']
            )
            
            # İşlemi çalıştır
            results = processor.process_geotiff(input_file)
            
            if results:
                print(f"✅ {config['name']} testi başarılı!")
                print(f"   📄 Çıktılar: {len(results)} dosya oluşturuldu")
                print(f"   🎨 Kullanılan renk: {results.get('num_colors_used', 'N/A')}")
                if results.get('field_area_hectares'):
                    print(f"   🌾 Tarla alanı: {results['field_area_hectares']:.1f} hektar")
                print(f"   📊 Kapsama: %{results.get('coverage_percentage', 0):.1f}")
            else:
                print(f"❌ {config['name']} testi başarısız!")
                
        except Exception as e:
            print(f"❌ {config['name']} testi hatası: {e}")
            import traceback
            traceback.print_exc()
    
    return True

def main():
    """Ana test fonksiyonu"""
    parser = argparse.ArgumentParser(description='Enhanced GeoTIFF Pipeline Test')
    parser.add_argument('--input', help='Test için kullanılacak GeoTIFF dosyası')
    parser.add_argument('--colors', type=int, default=24, help='Test renk sayısı')
    
    args = parser.parse_args()
    
    if args.input:
        # Tek dosya testi
        if not os.path.exists(args.input):
            print(f"❌ Dosya bulunamadı: {args.input}")
            return
        
        print(f"🧪 Tek dosya testi: {args.input}")
        print(f"🎨 Renk sayısı: {args.colors}")
        
        try:
            processor = GeotiffProcessor(
                config_path='configs/cod-sam-vit-h.yaml',
                checkpoint_path='save/checkpoint_14.pth', 
                num_colors=args.colors
            )
            
            results = processor.process_geotiff(args.input)
            
            if results:
                print("✅ Test başarılı!")
                print("📁 Çıktı dosyaları:")
                for key, path in results.items():
                    if isinstance(path, str) and os.path.exists(path):
                        print(f"   📄 {key}: {path}")
            else:
                print("❌ Test başarısız!")
                
        except Exception as e:
            print(f"❌ Test hatası: {e}")
            import traceback
            traceback.print_exc()
    else:
        # Otomatik test
        print("🚀 Enhanced GeoTIFF Pipeline Otomatik Test")
        print("=" * 50)
        test_enhanced_pipeline()

if __name__ == "__main__":
    main()
