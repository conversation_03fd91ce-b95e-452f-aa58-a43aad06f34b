#!/usr/bin/env python3
"""
K-means Enhanced Fast Batch Processor Test Script
Bu script yeni eklenen özellikleri test eder:
1. K-means clustering ile renk gruplandırması
2. Kullanıcı tarafından belirlenen grup sayısı
3. <PERSON><PERSON> alanları filtreleme
"""

import os
import sys

def test_kmeans_features():
    """Yeni K-means özelliklerini test et"""
    
    print("🧪 K-MEANS ENHANCED FEATURES TEST")
    print("=" * 50)
    
    # Test parametreleri
    test_cases = [
        {
            "name": "5 Grup K-means + Kenar Filtreleme",
            "args": "--num_groups 5 --filter_edges",
            "description": "5 renk grubu ile K-means clustering, kenar alanları filtrele"
        },
        {
            "name": "8 Grup K-means + Kenar Filtreleme Kapalı", 
            "args": "--num_groups 8 --no_filter_edges",
            "description": "8 renk grubu ile K-means clustering, kenar filtreleme kapalı"
        },
        {
            "name": "10 Grup K-means + <PERSON>ar Filtreleme",
            "args": "--num_groups 10 --filter_edges",
            "description": "10 renk grubu ile K-means clustering, kenar alanları filtrele"
        },
        {
            "name": "Otomatik Gruplandırma + Kenar Filtreleme",
            "args": "--filter_edges",
            "description": "Otomatik grup sayısı belirleme, kenar alanları filtrele"
        },
        {
            "name": "Geleneksel Mod (Kenar Filtreleme Kapalı)",
            "args": "--no_filter_edges",
            "description": "Eski spatial+renk benzerliği algoritması, kenar filtreleme kapalı"
        }
    ]
    
    # Test dosyası kontrolü
    test_files = [
        "test_images",  # Klasör modu için
        "geotiff_input/asd_center_5k.tif",  # Tek dosya modu için (eğer varsa)
    ]
    
    available_test_files = []
    for test_file in test_files:
        if os.path.exists(test_file):
            available_test_files.append(test_file)
            print(f"✅ Test dosyası bulundu: {test_file}")
        else:
            print(f"⚠️ Test dosyası bulunamadı: {test_file}")
    
    if not available_test_files:
        print("❌ Hiç test dosyası bulunamadı!")
        print("Lütfen test_images klasörü veya geotiff_input/asd_center_5k.tif dosyası ekleyin.")
        return
    
    print(f"\n📋 {len(test_cases)} test senaryosu hazırlandı:")
    for i, test_case in enumerate(test_cases, 1):
        print(f"  {i}. {test_case['name']}")
        print(f"     Args: {test_case['args']}")
        print(f"     Açıklama: {test_case['description']}")
        print()
    
    # Kullanıcıdan test seçimi iste
    print("🎯 Hangi testi çalıştırmak istiyorsunuz?")
    print("0. Tüm testleri çalıştır")
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. {test_case['name']}")
    
    try:
        choice = input("\nSeçiminizi yapın (0-5): ").strip()
        choice = int(choice)
        
        if choice == 0:
            # Tüm testleri çalıştır
            print("\n🚀 TÜM TESTLER ÇALIŞTIRILACAK")
            selected_tests = test_cases
        elif 1 <= choice <= len(test_cases):
            # Seçilen testi çalıştır
            selected_tests = [test_cases[choice - 1]]
            print(f"\n🚀 SEÇİLEN TEST: {selected_tests[0]['name']}")
        else:
            print("❌ Geçersiz seçim!")
            return
            
    except (ValueError, KeyboardInterrupt):
        print("\n❌ Test iptal edildi.")
        return
    
    # Test dosyası seçimi
    if len(available_test_files) > 1:
        print(f"\n📁 Hangi test dosyasını kullanmak istiyorsunuz?")
        for i, test_file in enumerate(available_test_files):
            print(f"{i+1}. {test_file}")
        
        try:
            file_choice = input(f"Seçiminizi yapın (1-{len(available_test_files)}): ").strip()
            file_choice = int(file_choice) - 1
            
            if 0 <= file_choice < len(available_test_files):
                selected_file = available_test_files[file_choice]
            else:
                print("❌ Geçersiz dosya seçimi!")
                return
        except (ValueError, KeyboardInterrupt):
            print("\n❌ Test iptal edildi.")
            return
    else:
        selected_file = available_test_files[0]
    
    print(f"\n📄 Seçilen test dosyası: {selected_file}")
    
    # Testleri çalıştır
    for i, test_case in enumerate(selected_tests):
        print(f"\n{'='*60}")
        print(f"🧪 TEST {i+1}/{len(selected_tests)}: {test_case['name']}")
        print(f"{'='*60}")
        print(f"📝 Açıklama: {test_case['description']}")
        print(f"⚙️ Parametreler: {test_case['args']}")
        
        # Output klasörü oluştur
        output_dir = f"test_output_{i+1}_{test_case['name'].lower().replace(' ', '_').replace('+', '_')}"
        
        # Komut oluştur
        if os.path.isdir(selected_file):
            # Klasör modu
            cmd = f"python fast_batch_processor.py --mode batch --input_dir {selected_file} --output_dir {output_dir} {test_case['args']}"
        else:
            # Tek dosya modu
            cmd = f"python fast_batch_processor.py --mode single --single_file {selected_file} --output_dir {output_dir} {test_case['args']}"
        
        print(f"🚀 Komut: {cmd}")
        print(f"📁 Çıktı klasörü: {output_dir}")
        
        # Kullanıcıdan onay iste
        if len(selected_tests) > 1:
            response = input("Bu testi çalıştırmak için Enter'a basın (s=atla, q=çık): ").strip().lower()
            if response == 's':
                print("⏭️ Test atlandı.")
                continue
            elif response == 'q':
                print("🛑 Testler durduruldu.")
                break
        
        # Komutu çalıştır
        print("⏳ Test çalıştırılıyor...")
        exit_code = os.system(cmd)
        
        if exit_code == 0:
            print(f"✅ Test başarılı! Sonuçlar: {output_dir}")
        else:
            print(f"❌ Test başarısız! Exit code: {exit_code}")
        
        print(f"{'='*60}")
    
    print("\n🎉 TEST TAMAMLANDI!")
    print("📊 Sonuçları karşılaştırmak için output klasörlerini kontrol edin.")

if __name__ == "__main__":
    test_kmeans_features()
