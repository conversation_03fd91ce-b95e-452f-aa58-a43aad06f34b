import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models

class AttentionBlock(nn.Module):
    """Attention mechanism for better feature refinement"""
    def __init__(self, in_channels):
        super().__init__()
        self.attention = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, 1),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, in_channels, 1),
            nn.BatchNorm2d(in_channels),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        att = self.attention(x)
        return x * att

class DoubleConv(nn.Module):
    """(convolution => [BN] => ReLU) * 2"""
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)

class Down(nn.Module):
    """Downscaling with maxpool then double conv"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)

class Up(nn.Module):
    """Upscaling then double conv"""
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        
        # Boyutları eşitle
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        
        # Skip connection
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

class OutConv(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(OutConv, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=1)

    def forward(self, x):
        return self.conv(x)

# Gelişmiş ResNet-UNet Modeli
class ResNetUNet(nn.Module):
    """High-performance ResNet-UNet for binary refinement with attention"""
    def __init__(self, n_channels=3, n_classes=1, backbone='resnet34'):
        super().__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        
        # ResNet backbone
        if backbone == 'resnet34':
            resnet = models.resnet34(pretrained=True)
            filters = [64, 64, 128, 256, 512]
        elif backbone == 'resnet50':
            resnet = models.resnet50(pretrained=True)
            filters = [64, 256, 512, 1024, 2048]
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")
        
        # ResNet encoder layers
        self.firstconv = resnet.conv1
        self.firstbn = resnet.bn1
        self.firstrelu = resnet.relu
        self.firstmaxpool = resnet.maxpool
        self.encoder1 = resnet.layer1
        self.encoder2 = resnet.layer2
        self.encoder3 = resnet.layer3
        self.encoder4 = resnet.layer4
        
        # Attention blocks
        self.att1 = AttentionBlock(filters[1])
        self.att2 = AttentionBlock(filters[2])
        self.att3 = AttentionBlock(filters[3])
        self.att4 = AttentionBlock(filters[4])
        
        # Decoder (UNet-style)
        self.upconv4 = nn.ConvTranspose2d(filters[4], filters[3], 2, stride=2)
        self.decoder4 = DoubleConv(filters[4], filters[3])
        
        self.upconv3 = nn.ConvTranspose2d(filters[3], filters[2], 2, stride=2)
        self.decoder3 = DoubleConv(filters[3], filters[2])
        
        self.upconv2 = nn.ConvTranspose2d(filters[2], filters[1], 2, stride=2)
        self.decoder2 = DoubleConv(filters[2], filters[1])
        
        self.upconv1 = nn.ConvTranspose2d(filters[1], filters[0], 2, stride=2)
        self.decoder1 = DoubleConv(filters[1], filters[0])
        
        # Final classifier
        self.final_conv = nn.Sequential(
            nn.Conv2d(filters[0], 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, padding=1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, n_classes, 1),
            nn.Sigmoid()
        )
        
        # Input channel adjustment
        if n_channels != 3:
            self.firstconv = nn.Conv2d(n_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
    
    def forward(self, x):
        # Encoder
        e0 = self.firstconv(x)      # [B, 64, H/2, W/2]
        e0 = self.firstbn(e0)
        e0 = self.firstrelu(e0)
        e0 = self.firstmaxpool(e0)  # [B, 64, H/4, W/4]
        
        e1 = self.encoder1(e0)      # [B, 64, H/4, W/4]
        e1 = self.att1(e1)
        
        e2 = self.encoder2(e1)      # [B, 128, H/8, W/8]
        e2 = self.att2(e2)
        
        e3 = self.encoder3(e2)      # [B, 256, H/16, W/16]
        e3 = self.att3(e3)
        
        e4 = self.encoder4(e3)      # [B, 512, H/32, W/32]
        e4 = self.att4(e4)
        
        # Decoder
        d4 = self.upconv4(e4)       # [B, 256, H/16, W/16]
        # Boyut eşitle
        if d4.size() != e3.size():
            d4 = F.interpolate(d4, size=e3.shape[2:], mode='bilinear', align_corners=True)
        d4 = torch.cat([e3, d4], dim=1)  # [B, 512, H/16, W/16]
        d4 = self.decoder4(d4)      # [B, 256, H/16, W/16]
        
        d3 = self.upconv3(d4)       # [B, 128, H/8, W/8]
        if d3.size() != e2.size():
            d3 = F.interpolate(d3, size=e2.shape[2:], mode='bilinear', align_corners=True)
        d3 = torch.cat([e2, d3], dim=1)  # [B, 256, H/8, W/8]
        d3 = self.decoder3(d3)      # [B, 128, H/8, W/8]
        
        d2 = self.upconv2(d3)       # [B, 64, H/4, W/4]
        if d2.size() != e1.size():
            d2 = F.interpolate(d2, size=e1.shape[2:], mode='bilinear', align_corners=True)
        d2 = torch.cat([e1, d2], dim=1)  # [B, 128, H/4, W/4]
        d2 = self.decoder2(d2)      # [B, 64, H/4, W/4]
        
        d1 = self.upconv1(d2)       # [B, 64, H/2, W/2]
        if d1.size() != e0.size():
            d1 = F.interpolate(d1, size=e0.shape[2:], mode='bilinear', align_corners=True)
        d1 = torch.cat([e0, d1], dim=1)  # [B, 128, H/2, W/2]
        d1 = self.decoder1(d1)      # [B, 64, H/2, W/2]
        
        # Final upsampling to original size
        d1 = F.interpolate(d1, size=(x.shape[2], x.shape[3]), mode='bilinear', align_corners=True)
        
        # Final output
        out = self.final_conv(d1)   # [B, 1, H, W]
        return out

# Orijinal basit modeller (backward compatibility için)
class SimpleBinaryRefinement(nn.Module):
    """Basit ve hızlı binary refinement modeli"""
    def __init__(self, n_channels=3, n_classes=1):
        super().__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        
        # Encoder
        self.inc = DoubleConv(n_channels, 64)
        self.down1 = Down(64, 128)
        self.down2 = Down(128, 256)
        self.down3 = Down(256, 512)
        
        # Decoder
        self.up1 = Up(512 + 256, 256)
        self.up2 = Up(256 + 128, 128)
        self.up3 = Up(128 + 64, 64)
        self.outc = OutConv(64, n_classes)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        
        x = self.up1(x4, x3)
        x = self.up2(x, x2)
        x = self.up3(x, x1)
        
        logits = self.outc(x)
        return self.sigmoid(logits)

class BinaryRefinementUNet(nn.Module):
    def __init__(self, n_channels, n_classes, bilinear=False):
        super(BinaryRefinementUNet, self).__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        self.bilinear = bilinear

        self.inc = DoubleConv(n_channels, 64)
        self.down1 = Down(64, 128)
        self.down2 = Down(128, 256)
        self.down3 = Down(256, 512)
        factor = 2 if bilinear else 1
        self.down4 = Down(512, 1024 // factor)
        self.up1 = Up(1024, 512 // factor, bilinear)
        self.up2 = Up(512, 256 // factor, bilinear)
        self.up3 = Up(256, 128 // factor, bilinear)
        self.up4 = Up(128, 64, bilinear)
        self.outc = OutConv(64, n_classes)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)
        logits = self.outc(x)
        return self.sigmoid(logits)

# Gelişmiş loss function'ları
class CombinedLoss(nn.Module):
    """Focal + Dice + BCE loss combination for better performance"""
    def __init__(self, alpha=0.25, gamma=2.0, dice_weight=0.5, bce_weight=0.3, focal_weight=0.2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.dice_weight = dice_weight
        self.bce_weight = bce_weight
        self.focal_weight = focal_weight
        self.bce = nn.BCELoss()
    
    def focal_loss(self, pred, target):
        """Focal loss for addressing class imbalance"""
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * bce_loss
        return focal_loss.mean()
    
    def dice_loss(self, pred, target, smooth=1e-7):
        """Dice loss for better IoU optimization"""
        # Flatten tensors
        pred_flat = pred.contiguous().view(-1)
        target_flat = target.contiguous().view(-1)
        
        intersection = (pred_flat * target_flat).sum()
        dice_score = (2. * intersection + smooth) / (pred_flat.sum() + target_flat.sum() + smooth)
        return 1 - dice_score
    
    def forward(self, pred, target):
        dice = self.dice_loss(pred, target)
        bce = self.bce(pred, target)
        focal = self.focal_loss(pred, target)
        
        total_loss = (self.dice_weight * dice + 
                     self.bce_weight * bce + 
                     self.focal_weight * focal)
        return total_loss 