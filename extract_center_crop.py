#!/usr/bin/env python3
"""
TIFF dosyasının ortasından 5K x 5K kesit alma scripti
"""

import os
import rasterio
from rasterio.windows import Window
import numpy as np

def extract_center_crop(input_path, output_path, crop_size=5000):
    """
    TIFF dosyasının ortasından belirtilen boyutta kesit al
    
    Args:
        input_path: Giriş TIFF dosyası
        output_path: Çıkış TIFF dosyası 
        crop_size: Kesit boyutu (piksel)
    """
    
    print(f"📁 Dosya açılıyor: {input_path}")
    
    if not os.path.exists(input_path):
        print(f"❌ Dosya bulunamadı: {input_path}")
        return False
    
    try:
        with rasterio.open(input_path) as src:
            height, width = src.height, src.width
            
            print(f"📊 Orijinal boyut: {height} x {width}")
            print(f"🎯 Kesit boyutu: {crop_size} x {crop_size}")
            
            # Kesit boyutu kontrolü
            if height < crop_size or width < crop_size:
                print(f"❌ Dosya kesit boyutundan küçük!")
                return False
            
            # Orta nokta hesaplama
            center_y = height // 2
            center_x = width // 2
            
            # Kesit başlangıç noktaları
            start_y = center_y - crop_size // 2
            start_x = center_x - crop_size // 2
            
            # Sınır kontrolü
            if start_y < 0:
                start_y = 0
            if start_x < 0:
                start_x = 0
            if start_y + crop_size > height:
                start_y = height - crop_size
            if start_x + crop_size > width:
                start_x = width - crop_size
            
            print(f"📍 Kesit koordinatları: ({start_x}, {start_y}) -> ({start_x + crop_size}, {start_y + crop_size})")
            
            # Window tanımla
            window = Window(start_x, start_y, crop_size, crop_size)
            
            # Kesit verilerini oku
            print("📖 Kesit okunuyor...")
            crop_data = src.read(window=window)
            
            # Profil kopyala ve güncelle
            profile = src.profile.copy()
            profile.update({
                'height': crop_size,
                'width': crop_size,
                'transform': rasterio.windows.transform(window, src.transform)
            })
            
            # Çıkış dosyasına yaz
            print(f"💾 Kesit kaydediliyor: {output_path}")
            with rasterio.open(output_path, 'w', **profile) as dst:
                dst.write(crop_data)
            
            # Dosya boyutu bilgisi
            output_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            print(f"✅ Başarıyla kaydedildi!")
            print(f"📄 Çıkış dosyası: {output_path}")
            print(f"📊 Çıkış boyutu: {crop_size}x{crop_size} piksel ({output_size_mb:.1f} MB)")
            
            return True
            
    except Exception as e:
        print(f"❌ Hata: {e}")
        return False

def main():
    """Ana fonksiyon"""
    import argparse
    
    parser = argparse.ArgumentParser(description='TIFF dosyasından orta kesit alma')
    parser.add_argument('--input', default='geotiff_input/asd.tif', 
                       help='Giriş TIFF dosyası')
    parser.add_argument('--output', default='geotiff_input/asd_center_5k.tif',
                       help='Çıkış TIFF dosyası')
    parser.add_argument('--size', type=int, default=5000,
                       help='Kesit boyutu (piksel)')
    
    args = parser.parse_args()
    
    success = extract_center_crop(args.input, args.output, args.size)
    
    if success:
        print("🎉 İşlem başarıyla tamamlandı!")
    else:
        print("❌ İşlem başarısız!")

if __name__ == "__main__":
    main() 