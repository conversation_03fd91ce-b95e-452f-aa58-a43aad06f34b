#!/usr/bin/env python3
"""
Test script to verify the fixes for K-means clustering and crop-based coloring
"""

import sys
import os
import numpy as np
import cv2
from PIL import Image

# Test the fixed functions
def test_extract_field_based_features():
    """Test the robust feature extraction"""
    print("🧪 Testing _extract_field_based_features...")
    
    # Create a dummy image and contours
    dummy_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    
    # Create some test contours
    contours = []
    # Green area (simulating crops)
    green_contour = np.array([[[100, 100]], [[200, 100]], [[200, 200]], [[100, 200]]], dtype=np.int32)
    contours.append(green_contour)
    
    # Yellow area (simulating mature crops)
    yellow_contour = np.array([[[300, 300]], [[400, 300]], [[400, 400]], [[300, 400]]], dtype=np.int32)
    contours.append(yellow_contour)
    
    # Fill the areas with appropriate colors
    cv2.fillPoly(dummy_image, [green_contour], (50, 150, 50))  # Green
    cv2.fillPoly(dummy_image, [yellow_contour], (200, 200, 50))  # Yellow
    
    # Import the processor
    sys.path.append('.')
    from fast_batch_processor import ExactTestResultProcessor
    
    processor = ExactTestResultProcessor()
    
    try:
        features, valid_contours = processor._extract_field_based_features(contours, dummy_image)
        
        if len(features) > 0:
            print(f"✅ Feature extraction successful: {features.shape}")
            print(f"✅ Valid contours: {len(valid_contours)}")
            print(f"📊 Feature range: min={np.min(features):.3f}, max={np.max(features):.3f}")
            return True
        else:
            print("❌ No features extracted")
            return False
            
    except Exception as e:
        print(f"❌ Feature extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crop_based_colors():
    """Test the crop-based color generation"""
    print("\n🧪 Testing generate_crop_based_colors...")
    
    # Create dummy data
    dummy_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    
    # Create test contours and color groups
    contours = []
    green_contour = np.array([[[100, 100]], [[200, 100]], [[200, 200]], [[100, 200]]], dtype=np.int32)
    yellow_contour = np.array([[[300, 300]], [[400, 300]], [[400, 400]], [[300, 400]]], dtype=np.int32)
    contours.extend([green_contour, yellow_contour])
    
    # Fill with crop colors
    cv2.fillPoly(dummy_image, [green_contour], (50, 150, 50))  # Green crop
    cv2.fillPoly(dummy_image, [yellow_contour], (200, 200, 50))  # Yellow crop
    
    # Create color groups
    color_groups = [[0], [1]]  # Each contour in separate group
    
    # Import the processor
    from fast_batch_processor import ExactTestResultProcessor
    
    processor = ExactTestResultProcessor()
    
    try:
        colors = processor.generate_crop_based_colors(color_groups, contours, dummy_image)
        
        if len(colors) > 0:
            print(f"✅ Color generation successful: {len(colors)} colors")
            for i, color in enumerate(colors):
                print(f"  Color {i}: RGB{color}")
            return True
        else:
            print("❌ No colors generated")
            return False
            
    except Exception as e:
        print(f"❌ Color generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_kmeans_clustering():
    """Test the robust K-means clustering"""
    print("\n🧪 Testing _kmeans_clustering_contours...")
    
    # Create dummy data
    dummy_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    
    # Create multiple test contours
    contours = []
    for i in range(5):
        x = i * 80 + 50
        y = 100
        contour = np.array([[[x, y]], [[x+60, y]], [[x+60, y+60]], [[x, y+60]]], dtype=np.int32)
        contours.append(contour)
        
        # Fill with different crop colors
        if i < 2:
            color = (50, 150, 50)  # Green crops
        elif i < 4:
            color = (200, 200, 50)  # Yellow crops
        else:
            color = (150, 100, 50)  # Brown crops
            
        cv2.fillPoly(dummy_image, [contour], color)
    
    # Import the processor
    from fast_batch_processor import ExactTestResultProcessor
    
    processor = ExactTestResultProcessor()
    
    try:
        color_groups = processor._kmeans_clustering_contours(contours, dummy_image, target_groups=3)
        
        if len(color_groups) > 0:
            print(f"✅ K-means clustering successful: {len(color_groups)} groups")
            for i, group in enumerate(color_groups):
                print(f"  Group {i}: {len(group)} contours - {group}")
            return True
        else:
            print("❌ No groups created")
            return False
            
    except Exception as e:
        print(f"❌ K-means clustering failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing fixed K-means and crop-based coloring...")
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Feature extraction
    if test_extract_field_based_features():
        success_count += 1
    
    # Test 2: Crop-based colors
    if test_crop_based_colors():
        success_count += 1
    
    # Test 3: K-means clustering
    if test_kmeans_clustering():
        success_count += 1
    
    print(f"\n📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed! The fixes should work correctly.")
    else:
        print("⚠️ Some tests failed. Check the error messages above.")
